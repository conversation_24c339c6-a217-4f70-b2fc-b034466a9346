<!DOCTYPE html>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布商品 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-right: 15px;
        }
        
        .page-title {
            color: #0c4da2;
            font-size: 32px;
            margin-bottom: 0;
        }
        
        .back-button {
            display: inline-block;
            color: #0c4da2;
            text-decoration: none;
            font-size: 16px;
            background-color: #fff;
            padding: 8px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background-color: #f5f5f5;
        }

        .admin-info {
            display: flex;
            align-items: center;
        }
        
        .admin-avatar {
            width: 40px;
            height: 40px;
            background-color: #0c4da2;
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .logout-btn {
            color: #0c4da2;
            text-decoration: none;
            margin-left: 15px;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        
        .logout-btn:hover {
            background-color: #f0f0f0;
        }
        
        .form-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .form-title {
            color: #0c4da2;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        .form-control {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            border-color: #0c4da2;
            outline: none;
        }
        
        textarea.form-control {
            min-height: 150px;
            resize: vertical;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 30px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: #0c4da2;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0a3d82;
        }
        
        .btn-secondary {
            background-color: #f0f0f0;
            color: #333;
            margin-right: 10px;
        }
        
        .btn-secondary:hover {
            background-color: #e0e0e0;
        }
        
        .image-upload-container {
            border: 2px dashed #ddd;
            padding: 30px;
            text-align: center;
            border-radius: 5px;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        
        .image-upload-container:hover {
            border-color: #0c4da2;
        }
        
        .image-upload-icon {
            font-size: 40px;
            color: #ddd;
            margin-bottom: 15px;
        }
        
        .image-upload-text {
            color: #777;
        }
        
        .image-upload-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .image-upload-item {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .image-upload-container {
            border: 2px dashed #ddd;
            padding: 20px 10px;
            text-align: center;
            border-radius: 5px;
            cursor: pointer;
            transition: border-color 0.3s;
            height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .image-preview-single {
            margin-top: 10px;
            position: relative;
            width: 100%;
        }
        
        .preview-item {
            position: relative;
            width: 100%;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .preview-item img {
            width: 100%;
            display: block;
            max-height: 200px;
            object-fit: contain;
            background-color: #f0f8ff;
        }
        
        .preview-remove {
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: rgba(255, 0, 0, 0.7);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            z-index: 10;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        
        .preview-remove:hover {
            background-color: rgba(255, 0, 0, 0.9);
        }
        
        @media (max-width: 992px) {
            .image-upload-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        @media (max-width: 576px) {
            .image-upload-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .toast {
            background-color: white;
            border-left: 4px solid #0c4da2;
            border-radius: 4px;
            padding: 15px 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            animation: slideIn 0.3s ease-out forwards;
            max-width: 300px;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        .toast-icon {
            margin-right: 10px;
            font-size: 18px;
        }
        
        .toast-content {
            flex-grow: 1;
        }
        
        .toast-title {
            font-weight: bold;
            margin-bottom: 3px;
        }
        
        .toast-message {
            font-size: 14px;
            color: #666;
        }
        
        .toast-success {
            border-left-color: #52c41a;
        }
        
        .toast-error {
            border-left-color: #ff4d4f;
        }
        
        .toast-warning {
            border-left-color: #faad14;
        }
        
        .toast-success .toast-icon {
            color: #52c41a;
        }
        
        .toast-error .toast-icon {
            color: #ff4d4f;
        }
        
        .toast-warning .toast-icon {
            color: #faad14;
        }
        
        /* 关键词搜索样式 */
        .keyword-search-container {
            position: relative;
        }

        .keyword-search-container .form-control {
            transition: all 0.3s ease;
            border: 2px solid #ddd;
        }

        .keyword-search-container .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .keyword-search-container .form-control:not(:placeholder-shown) {
            border-color: #667eea;
        }

        .keyword-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #667eea;
            border-top: none;
            border-radius: 0 0 8px 8px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
            animation: dropdownSlideDown 0.2s ease-out;
        }

        @keyframes dropdownSlideDown {
            0% {
                opacity: 0;
                transform: translateY(-10px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .keyword-dropdown.show {
            display: block;
        }

        .keyword-item {
            padding: 12px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.2s ease;
            position: relative;
            font-weight: 500;
        }

        .keyword-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transform: scaleY(0);
            transition: transform 0.2s ease;
        }

        .keyword-item:hover {
            background-color: #f8f9fa;
            padding-left: 20px;
        }

        .keyword-item:hover::before {
            transform: scaleY(1);
        }

        .keyword-item:last-child {
            border-bottom: none;
        }

        .keyword-item.disabled {
            color: #999;
            cursor: not-allowed;
            background-color: #f5f5f5;
        }

        .keyword-item.disabled:hover {
            padding-left: 15px;
        }

        .keyword-item.disabled::before {
            display: none;
        }

        .selected-keywords {
            margin-top: 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            min-height: 40px;
            padding: 10px;
            border: 1px dashed #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
            transition: all 0.3s ease;
        }

        .selected-keywords:empty::before {
            content: "暂未选择关键词";
            color: #999;
            font-style: italic;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 20px;
        }

        .keyword-tag {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .keyword-tag::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .keyword-tag:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .keyword-tag:hover::before {
            left: 100%;
        }

        .keyword-tag .remove-keyword {
            cursor: pointer;
            font-weight: bold;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.2s ease;
            margin-left: 2px;
        }

        .keyword-tag .remove-keyword:hover {
            background-color: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        /* 不同颜色的关键词标签 */
        .keyword-tag:nth-child(5n+1) {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .keyword-tag:nth-child(5n+2) {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            box-shadow: 0 2px 8px rgba(240, 147, 251, 0.3);
        }

        .keyword-tag:nth-child(5n+3) {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
        }

        .keyword-tag:nth-child(5n+4) {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            box-shadow: 0 2px 8px rgba(67, 233, 123, 0.3);
        }

        .keyword-tag:nth-child(5n+5) {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            box-shadow: 0 2px 8px rgba(250, 112, 154, 0.3);
        }

        /* 关键词添加动画 */
        @keyframes keywordFadeIn {
            0% {
                opacity: 0;
                transform: scale(0.8) translateY(-10px);
            }
            50% {
                transform: scale(1.05) translateY(-2px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .keyword-tag.new-keyword {
            animation: keywordFadeIn 0.4s ease-out forwards;
        }

        /* 关键词移除动画 */
        @keyframes keywordFadeOut {
            0% {
                opacity: 1;
                transform: scale(1);
            }
            100% {
                opacity: 0;
                transform: scale(0.8);
            }
        }

        .keyword-tag.removing {
            animation: keywordFadeOut 0.3s ease-in forwards;
        }

        /* 关键词容器为空时的样式 */
        .selected-keywords.empty {
            border-style: dashed;
            border-color: #d0d0d0;
        }

        .selected-keywords:not(.empty) {
            border-style: solid;
            border-color: #e0e0e0;
            background-color: #f8f9fa;
        }

        .no-keywords-found {
            padding: 20px 15px;
            text-align: center;
            color: #999;
            font-style: italic;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 4px;
            margin: 5px;
            position: relative;
        }

        .no-keywords-found::before {
            content: '🔍';
            display: block;
            font-size: 24px;
            margin-bottom: 8px;
            opacity: 0.5;
        }

        /* 商品类型样式 */
        .product-type-item {
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f9f9f9;
        }

        .type-input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .type-name-input {
            flex: 2;
        }

        .type-price-input {
            flex: 1;
        }

        .remove-type-btn {
            flex-shrink: 0;
            padding: 8px 12px;
            font-size: 14px;
        }

        #addTypeBtn {
            margin-top: 10px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }

            .header {
                flex-direction: column;
                align-items: flex-start;
            }

            .admin-info {
                margin-top: 15px;
            }

            .form-card {
                padding: 20px;
            }

            .type-input-group {
                flex-direction: column;
                gap: 8px;
            }

            .type-name-input,
            .type-price-input {
                flex: none;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo-container">
                <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <h1 class="page-title">发布商品</h1>
            </div>
            <div class="admin-info">
                <div class="admin-avatar" id="adminInitial">A</div>
                <span id="adminName">管理员</span>
                <a href="#" class="logout-btn" id="logoutBtn">
                    <i class="fas fa-sign-out-alt"></i> 退出
                </a>
                <a href="admin-dashboard.html" class="back-button">
                    <i class="fas fa-arrow-left"></i> 返回控制台
                </a>
            </div>
        </header>
        
        <div class="form-card">
            <h2 class="form-title">商品信息</h2>
            <form id="productForm">
                <div class="form-group">
                    <label for="productName">商品名称</label>
                    <input type="text" id="productName" name="productName" class="form-control" placeholder="请输入商品名称" required>
                </div>
                
                <div class="form-group">
                    <label for="productTypes">商品类型 <small>(最多可添加10个类型)</small></label>
                    <div id="productTypesContainer">
                        <div class="product-type-item" data-index="0">
                            <div class="type-input-group">
                                <input type="text" name="productTypeName[]" class="form-control type-name-input" placeholder="请输入商品类型名称" required oninput="saveProductTypes()">
                                <input type="number" name="productTypePrice[]" class="form-control type-price-input" placeholder="0.00" step="0.01" min="0" required oninput="saveProductTypes()">
                                <button type="button" class="btn btn-danger remove-type-btn" onclick="removeProductType(0)" style="display: none;">删除</button>
                            </div>
                        </div>
                    </div>
                    <button type="button" id="addTypeBtn" class="btn btn-secondary" onclick="addProductType()">
                        <i class="fas fa-plus"></i> 添加商品类型
                    </button>
                </div>

                <div class="form-group">
                    <label for="productKeywords">商品关键词 <small>(最多可选择5个)</small></label>
                    <div class="keyword-search-container">
                        <input type="text" id="keywordSearch" class="form-control" placeholder="搜索关键词..." autocomplete="off">
                        <div class="keyword-dropdown" id="keywordDropdown"></div>
                    </div>
                    <div class="selected-keywords" id="selectedKeywords"></div>
                </div>

                <div class="form-group">
                    <label for="productStock">库存数量</label>
                    <input type="number" id="productStock" name="productStock" class="form-control" placeholder="0" min="0" required>
                </div>
                
                <div class="form-group">
                    <label for="productDescription">商品描述</label>
                    <textarea id="productDescription" name="productDescription" class="form-control" placeholder="请输入商品详细描述..." required></textarea>
                </div>
                
                <div class="form-group">
                    <label>商品图片 <small>(自动压缩，建议每张不超过10MB)</small></label>
                    <div class="image-upload-grid">
                        <div class="image-upload-item" id="imageUploadItem1">
                            <div class="image-upload-container">
                                <div class="image-upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="image-upload-text">图片1</div>
                                <input type="file" class="product-image-input" name="productImage1" accept="image/*" style="display: none;">
                            </div>
                            <div class="image-preview-single" id="imagePreview1"></div>
                        </div>
                        <div class="image-upload-item" id="imageUploadItem2">
                            <div class="image-upload-container">
                                <div class="image-upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="image-upload-text">图片2</div>
                                <input type="file" class="product-image-input" name="productImage2" accept="image/*" style="display: none;">
                            </div>
                            <div class="image-preview-single" id="imagePreview2"></div>
                        </div>
                        <div class="image-upload-item" id="imageUploadItem3">
                            <div class="image-upload-container">
                                <div class="image-upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="image-upload-text">图片3</div>
                                <input type="file" class="product-image-input" name="productImage3" accept="image/*" style="display: none;">
                            </div>
                            <div class="image-preview-single" id="imagePreview3"></div>
                        </div>
                        <div class="image-upload-item" id="imageUploadItem4">
                            <div class="image-upload-container">
                                <div class="image-upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="image-upload-text">图片4</div>
                                <input type="file" class="product-image-input" name="productImage4" accept="image/*" style="display: none;">
                            </div>
                            <div class="image-preview-single" id="imagePreview4"></div>
                        </div>
                        <div class="image-upload-item" id="imageUploadItem5">
                            <div class="image-upload-container">
                                <div class="image-upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="image-upload-text">图片5</div>
                                <input type="file" class="product-image-input" name="productImage5" accept="image/*" style="display: none;">
                            </div>
                            <div class="image-preview-single" id="imagePreview5"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 上架状态选项已被移除 -->
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" id="cancelBtn">取消</button>
                    <button type="submit" class="btn btn-primary" id="submitBtn">发布商品</button>
                </div>
            </form>
        </div>
    </div>
    
    <div class="toast-container" id="toastContainer"></div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查管理员登录状态
            const checkAdminAuth = () => {
                const adminData = JSON.parse(sessionStorage.getItem('loggedInAdmin') || '{}');
                
                if (!adminData.isAdmin) {
                    // 未登录或不是管理员，重定向到登录页
                    window.location.href = 'admin-login.html';
                    return null;
                }
                
                return adminData;
            };

            // 保存商品数据到服务器
            const saveProductData = () => {
                try {
                    const adminData = JSON.parse(sessionStorage.getItem('loggedInAdmin') || '{}');

                    if (!adminData.username) {
                        return;
                    }

                    const form = document.getElementById('productForm');
                    if (!form) {
                        console.warn('表单元素未找到');
                        return;
                    }

                    // 安全地获取表单字段值
                    const nameInput = form.querySelector('input[name="productName"]');
                    const stockInput = form.querySelector('input[name="productStock"]');
                    const descriptionInput = form.querySelector('textarea[name="productDescription"]');

                    // 收集商品类型数据
                    const productTypes = [];
                    const typeNameInputs = form.querySelectorAll('input[name="productTypeName[]"]');
                    const typePriceInputs = form.querySelectorAll('input[name="productTypePrice[]"]');

                    for (let i = 0; i < typeNameInputs.length; i++) {
                        const typeName = typeNameInputs[i].value.trim();
                        const typePrice = parseFloat(typePriceInputs[i].value);

                        if (typeName && !isNaN(typePrice) && typePrice >= 0) {
                            productTypes.push({
                                name: typeName,
                                price: typePrice
                            });
                        }
                    }

                    const productData = {
                        name: nameInput ? nameInput.value : '',
                        types: productTypes, // 保存商品类型数组
                        keywords: selectedKeywords, // 保存选中的关键词数组
                        stock: stockInput ? stockInput.value : '',
                        description: descriptionInput ? descriptionInput.value : '',
                        images: []
                    };

                    // 收集图片信息
                    for (let i = 1; i <= 5; i++) {
                        const fileInput = form.querySelector(`input[name="productImage${i}"]`);
                        if (fileInput && fileInput.dataset.imageFilename) {
                            productData.images.push({
                                index: i,
                                filename: fileInput.dataset.imageFilename,
                                url: fileInput.dataset.imageUrl
                            });
                        }
                    }

                    // 使用API保存到服务器
                    fetch('/api/save-product-data', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: adminData.username,
                            productData: productData
                        })
                    })
                    .catch(error => {
                        console.error('保存商品数据错误:', error);
                    });
                } catch (error) {
                    console.error('保存商品数据失败:', error);
                    return;
                }
            };

            // 从服务器加载商品数据
            const loadProductData = () => {
                const adminData = JSON.parse(sessionStorage.getItem('loggedInAdmin') || '{}');

                if (!adminData.username) {
                    console.log('loadProductData: 未找到用户名，跳过加载');
                    return;
                }

                console.log('loadProductData: 开始加载用户数据:', adminData.username);

                // 使用API从服务器获取数据
                fetch(`/api/get-product-data?username=${encodeURIComponent(adminData.username)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        const productData = data.data;
                        console.log('恢复商品数据:', productData);

                        const form = document.getElementById('productForm');

                        if (!form) {
                            console.warn('表单元素未找到');
                            return;
                        }

                        // 安全地恢复表单字段
                        const nameInput = form.querySelector('input[name="productName"]');
                        const priceInput = form.querySelector('input[name="productPrice"]');
                        const keywordsInput = form.querySelector('input[name="productKeywords"]');
                        const stockInput = form.querySelector('input[name="productStock"]');
                        const descriptionInput = form.querySelector('textarea[name="productDescription"]');

                        if (nameInput) nameInput.value = productData.name || '';
                        if (priceInput) priceInput.value = productData.price || '';
                        if (stockInput) stockInput.value = productData.stock || '';
                        if (descriptionInput) descriptionInput.value = productData.description || '';

                        // 恢复选中的关键词
                        if (productData.keywords && Array.isArray(productData.keywords)) {
                            selectedKeywords = [...productData.keywords];
                            updateSelectedKeywordsDisplay();
                        }

                        // 恢复图片
                        if (productData.images && productData.images.length > 0) {
                            console.log('开始恢复图片，图片数量:', productData.images.length);
                            productData.images.forEach(imageInfo => {
                                console.log(`恢复图片${imageInfo.index}:`, imageInfo);
                                const fileInput = form.querySelector(`input[name="productImage${imageInfo.index}"]`);
                                if (fileInput && imageInfo.filename && imageInfo.url) {
                                    // 设置图片数据
                                    fileInput.dataset.imageFilename = imageInfo.filename;
                                    fileInput.dataset.imageUrl = imageInfo.url;

                                    // 获取相关元素
                                    const uploadItem = document.getElementById(`imageUploadItem${imageInfo.index}`);
                                    const uploadContainer = uploadItem.querySelector('.image-upload-container');
                                    const previewContainer = document.getElementById(`imagePreview${imageInfo.index}`);

                                    if (uploadContainer && previewContainer) {
                                        // 清空预览容器
                                        previewContainer.innerHTML = '';

                                        // 隐藏上传区域
                                        uploadContainer.style.display = 'none';

                                        // 创建预览元素
                                        const previewItem = document.createElement('div');
                                        previewItem.className = 'preview-item';

                                        const img = document.createElement('img');
                                        img.src = imageInfo.url;

                                        // 确保图片加载完成后显示完整
                                        img.onload = function() {
                                            console.log(`图片${imageInfo.index}加载成功:`, imageInfo.url);
                                            previewContainer.style.height = 'auto';
                                        };

                                        // 图片加载失败时清理无效引用
                                        img.onerror = function() {
                                            console.warn(`图片${imageInfo.index}加载失败: ${imageInfo.url}, 清理无效引用`);
                                            // 清理文件输入的数据
                                            delete fileInput.dataset.imageUrl;
                                            delete fileInput.dataset.imageFilename;
                                            // 显示上传容器
                                            uploadContainer.style.display = 'flex';
                                            // 清空预览区域
                                            previewContainer.innerHTML = '';
                                            previewContainer.style.height = '0';
                                            // 保存更新后的数据
                                            saveProductData();
                                        };

                                        const removeBtn = document.createElement('div');
                                        removeBtn.className = 'preview-remove';
                                        removeBtn.innerHTML = '<i class="fas fa-times"></i>';

                                        // 点击删除按钮时重置上传区域
                                        removeBtn.addEventListener('click', (e) => {
                                            e.stopPropagation();
                                            // 找到对应的重置函数并调用
                                            const resetFunction = window[`resetUploader${imageInfo.index}`];
                                            if (resetFunction) {
                                                resetFunction();
                                            }
                                        });

                                        previewItem.appendChild(img);
                                        previewItem.appendChild(removeBtn);
                                        previewContainer.appendChild(previewItem);
                                    }
                                }
                            });
                        }
                    }
                })
                .catch(error => {
                    console.error('加载商品数据错误:', error);
                });
            };

            // 初始化页面
            const initPage = () => {
                try {
                    const adminData = checkAdminAuth();
                    if (!adminData) return;

                    // 设置管理员信息
                    const adminInitial = document.getElementById('adminInitial');
                    const adminName = document.getElementById('adminName');

                    if (adminData.username && adminInitial && adminName) {
                        adminName.textContent = adminData.username;
                        adminInitial.textContent = adminData.username.charAt(0).toUpperCase();
                    }
                } catch (error) {
                    console.error('初始化管理员信息失败:', error);
                }
                
                try {
                    // 退出登录功能
                    const logoutBtn = document.getElementById('logoutBtn');
                    if (logoutBtn) {
                        logoutBtn.addEventListener('click', (e) => {
                            e.preventDefault();
                            sessionStorage.removeItem('loggedInAdmin');
                            sessionStorage.removeItem('welcomeShown');
                            window.location.href = 'admin-login.html';
                        });
                    }

                    // 取消按钮
                    const cancelBtn = document.getElementById('cancelBtn');
                    if (cancelBtn) {
                        cancelBtn.addEventListener('click', () => {
                            if (confirm('确定要取消发布吗？未保存的内容将丢失。')) {
                                // 清理所有已上传的图片和保存的数据
                                cleanupAllUploadedImages(() => {
                                    // 清理保存的商品数据
                                    fetch('/api/delete-product-data', {
                                        method: 'DELETE',
                                        headers: {
                                            'Content-Type': 'application/json'
                                        },
                                        body: JSON.stringify({
                                            username: adminData.username
                                        })
                                    })
                                    .then(() => {
                                        window.location.href = 'admin-dashboard.html';
                                    })
                                    .catch(() => {
                                        window.location.href = 'admin-dashboard.html';
                                    });
                                });
                            }
                        });
                    }
                } catch (error) {
                    console.error('初始化按钮事件失败:', error);
                }

                try {
                    // 图片上传处理
                    setupImageUpload();
                } catch (error) {
                    console.error('初始化图片上传失败:', error);
                }

                try {
                    // 关键词搜索功能
                    setupKeywordSearch();
                } catch (error) {
                    console.error('初始化关键词搜索失败:', error);
                }

                // 检查URL参数，判断是否为编辑模式
                const urlParams = new URLSearchParams(window.location.search);
                const editProductId = urlParams.get('edit');

                // 清除之前的编辑模式标记
                delete window.editingProductId;

                if (editProductId) {
                    // 编辑模式：加载指定商品数据
                    setTimeout(() => {
                        try {
                            loadProductForEdit(editProductId);
                            // 更改页面标题
                            const pageTitle = document.querySelector('.page-title');
                            const submitBtn = document.querySelector('#submitBtn');
                            if (pageTitle) pageTitle.textContent = '编辑商品';
                            if (submitBtn) submitBtn.textContent = '更新商品';
                        } catch (error) {
                            console.error('加载编辑商品数据失败:', error);
                        }
                    }, 500);
                } else {
                    // 发布模式：加载保存的草稿数据
                    setTimeout(() => {
                        try {
                            loadProductData();
                        } catch (error) {
                            console.error('加载商品数据失败:', error);
                        }
                    }, 500); // 延迟加载确保所有组件都已初始化
                }

                try {
                    // 为表单字段添加自动保存监听器
                    const form = document.getElementById('productForm');
                    if (form) {
                        form.querySelectorAll('input, textarea').forEach(element => {
                            element.addEventListener('input', () => {
                                // 延迟保存，避免频繁调用
                                clearTimeout(window.saveTimeout);
                                window.saveTimeout = setTimeout(() => {
                                    try {
                                        saveProductData();
                                    } catch (error) {
                                        console.error('自动保存失败:', error);
                                    }
                                }, 1000);
                            });
                            element.addEventListener('change', () => {
                                try {
                                    saveProductData();
                                } catch (error) {
                                    console.error('保存数据失败:', error);
                                }
                            });
                        });

                        // 表单提交处理
                        form.addEventListener('submit', handleFormSubmit);
                    }
                } catch (error) {
                    console.error('初始化表单事件失败:', error);
                }
            };

            // 关键词搜索功能
            let allKeywords = [];
            let selectedKeywords = [];
            const maxKeywords = 5;

            const setupKeywordSearch = async () => {
                try {
                    // 加载所有关键词
                    await loadAllKeywords();

                    const keywordSearch = document.getElementById('keywordSearch');
                    const keywordDropdown = document.getElementById('keywordDropdown');

                    if (!keywordSearch || !keywordDropdown) {
                        console.warn('关键词搜索元素未找到');
                        return;
                    }

                    // 搜索输入事件
                    keywordSearch.addEventListener('input', (e) => {
                        try {
                            const searchTerm = e.target.value.trim().toLowerCase();
                            filterAndShowKeywords(searchTerm);
                        } catch (error) {
                            console.error('关键词搜索输入处理失败:', error);
                        }
                    });

                    // 点击外部关闭下拉框
                    document.addEventListener('click', (e) => {
                        try {
                            if (!e.target.closest('.keyword-search-container')) {
                                keywordDropdown.classList.remove('show');
                            }
                        } catch (error) {
                            console.error('关键词下拉框关闭失败:', error);
                        }
                    });

                    // 获得焦点时显示下拉框
                    keywordSearch.addEventListener('focus', () => {
                        try {
                            if (keywordSearch.value.trim()) {
                                filterAndShowKeywords(keywordSearch.value.trim().toLowerCase());
                            }
                        } catch (error) {
                            console.error('关键词搜索焦点处理失败:', error);
                        }
                    });
                } catch (error) {
                    console.error('设置关键词搜索失败:', error);
                }
            };

            // 加载所有关键词
            const loadAllKeywords = async () => {
                try {
                    const response = await fetch('/api/keywords');
                    const result = await response.json();

                    if (result.success) {
                        allKeywords = result.keywords || [];
                    } else {
                        console.error('获取关键词失败:', result.message);
                        // 如果API失败，使用模拟数据
                        allKeywords = [
                            { id: '1', text: '电子产品', addedAt: new Date().toISOString() },
                            { id: '2', text: '手机配件', addedAt: new Date().toISOString() },
                            { id: '3', text: '数码相机', addedAt: new Date().toISOString() },
                            { id: '4', text: '笔记本电脑', addedAt: new Date().toISOString() },
                            { id: '5', text: '智能手表', addedAt: new Date().toISOString() },
                            { id: '6', text: '蓝牙耳机', addedAt: new Date().toISOString() },
                            { id: '7', text: '充电器', addedAt: new Date().toISOString() },
                            { id: '8', text: '数据线', addedAt: new Date().toISOString() }
                        ];
                    }
                } catch (error) {
                    console.error('加载关键词失败:', error);
                    // 使用模拟数据
                    allKeywords = [
                        { id: '1', text: '电子产品', addedAt: new Date().toISOString() },
                        { id: '2', text: '手机配件', addedAt: new Date().toISOString() },
                        { id: '3', text: '数码相机', addedAt: new Date().toISOString() },
                        { id: '4', text: '笔记本电脑', addedAt: new Date().toISOString() },
                        { id: '5', text: '智能手表', addedAt: new Date().toISOString() },
                        { id: '6', text: '蓝牙耳机', addedAt: new Date().toISOString() },
                        { id: '7', text: '充电器', addedAt: new Date().toISOString() },
                        { id: '8', text: '数据线', addedAt: new Date().toISOString() }
                    ];
                }
            };

            // 过滤并显示关键词
            const filterAndShowKeywords = (searchTerm) => {
                try {
                    const keywordDropdown = document.getElementById('keywordDropdown');

                    if (!keywordDropdown) {
                        console.warn('关键词下拉框元素未找到');
                        return;
                    }

                    if (!searchTerm) {
                        keywordDropdown.classList.remove('show');
                        return;
                    }
                } catch (error) {
                    console.error('过滤关键词失败:', error);
                    return;
                }

                // 过滤关键词
                const filteredKeywords = allKeywords.filter(keyword =>
                    keyword.text.toLowerCase().includes(searchTerm) &&
                    !selectedKeywords.some(selected => selected.id === keyword.id)
                );

                // 显示下拉框
                if (filteredKeywords.length > 0) {
                    keywordDropdown.innerHTML = filteredKeywords.map(keyword => `
                        <div class="keyword-item ${selectedKeywords.length >= maxKeywords ? 'disabled' : ''}"
                             onclick="selectKeyword('${keyword.id}', '${keyword.text}')">
                            ${keyword.text}
                        </div>
                    `).join('');
                    keywordDropdown.classList.add('show');
                } else {
                    keywordDropdown.innerHTML = '<div class="no-keywords-found">未找到匹配的关键词</div>';
                    keywordDropdown.classList.add('show');
                }
            };

            // 选择关键词 - 定义为全局函数
            window.selectKeyword = (keywordId, keywordText) => {
                // 检查是否已达到最大数量
                if (selectedKeywords.length >= maxKeywords) {
                    showToast(`最多只能选择${maxKeywords}个关键词`, 'warning', '选择限制');
                    return;
                }

                // 检查是否已选择
                if (selectedKeywords.some(keyword => keyword.id === keywordId)) {
                    return;
                }

                // 添加到已选择列表
                selectedKeywords.push({ id: keywordId, text: keywordText });

                // 更新显示
                updateSelectedKeywordsDisplay();

                // 保存商品数据
                saveProductData();

                // 清空搜索框并隐藏下拉框
                const keywordSearchElement = document.getElementById('keywordSearch');
                const keywordDropdownElement = document.getElementById('keywordDropdown');

                if (keywordSearchElement) keywordSearchElement.value = '';
                if (keywordDropdownElement) keywordDropdownElement.classList.remove('show');
            };

            // 移除关键词 - 定义为全局函数
            window.removeKeyword = (keywordId) => {
                const keywordElement = document.querySelector(`[data-keyword-id="${keywordId}"]`);
                if (keywordElement) {
                    keywordElement.classList.add('removing');
                    setTimeout(() => {
                        selectedKeywords = selectedKeywords.filter(keyword => keyword.id !== keywordId);
                        updateSelectedKeywordsDisplay();
                        saveProductData(); // 保存商品数据
                    }, 300);
                } else {
                    selectedKeywords = selectedKeywords.filter(keyword => keyword.id !== keywordId);
                    updateSelectedKeywordsDisplay();
                    saveProductData(); // 保存商品数据
                }
            };

            // 更新已选择关键词的显示
            const updateSelectedKeywordsDisplay = () => {
                const selectedKeywordsContainer = document.getElementById('selectedKeywords');

                // 更新容器样式
                if (selectedKeywords.length === 0) {
                    selectedKeywordsContainer.innerHTML = '';
                    selectedKeywordsContainer.classList.add('empty');
                    selectedKeywordsContainer.classList.remove('not-empty');
                    return;
                } else {
                    selectedKeywordsContainer.classList.remove('empty');
                    selectedKeywordsContainer.classList.add('not-empty');
                }

                // 获取当前显示的关键词ID
                const currentKeywordIds = Array.from(selectedKeywordsContainer.querySelectorAll('.keyword-tag'))
                    .map(tag => tag.getAttribute('data-keyword-id'));

                // 找出新添加的关键词
                const newKeywords = selectedKeywords.filter(keyword =>
                    !currentKeywordIds.includes(keyword.id)
                );

                // 重新渲染所有关键词
                selectedKeywordsContainer.innerHTML = selectedKeywords.map((keyword, index) => `
                    <div class="keyword-tag ${newKeywords.some(nk => nk.id === keyword.id) ? 'new-keyword' : ''}"
                         data-keyword-id="${keyword.id}">
                        ${keyword.text}
                        <span class="remove-keyword" onclick="removeKeyword('${keyword.id}')">&times;</span>
                    </div>
                `).join('');

                // 移除动画类，避免重复触发
                setTimeout(() => {
                    selectedKeywordsContainer.querySelectorAll('.new-keyword').forEach(tag => {
                        tag.classList.remove('new-keyword');
                    });
                }, 400);
            };

                        // 图片上传功能
            const setupImageUpload = () => {
                // 设置每个上传区域
                for (let i = 1; i <= 5; i++) {
                    setupSingleImageUpload(i);
                }
            };
            
            // 单个图片上传区域设置
            const setupSingleImageUpload = (index) => {
                const uploadItem = document.getElementById(`imageUploadItem${index}`);
                const uploadContainer = uploadItem.querySelector('.image-upload-container');
                const fileInput = uploadItem.querySelector('.product-image-input');
                const previewContainer = document.getElementById(`imagePreview${index}`);
                
                // 初始化状态
                let hasUploadedFile = false;
                
                // 重置上传区域功能
                const resetUploader = () => {
                    console.log(`重置上传器 ${index}:`, {
                        hasFilename: !!fileInput.dataset.imageFilename,
                        filename: fileInput.dataset.imageFilename,
                        imageUrl: fileInput.dataset.imageUrl
                    });

                    // 如果有已上传的文件，先删除服务器上的文件
                    if (fileInput.dataset.imageFilename) {
                        console.log(`准备删除服务器文件: ${fileInput.dataset.imageFilename}`);
                        deleteImageFromServer(fileInput.dataset.imageFilename, () => {
                            console.log(`删除完成，清理本地状态`);
                            // 删除完成后清理本地状态
                            cleanupUploader();
                        });
                    } else {
                        console.log(`没有服务器文件需要删除，直接清理本地状态`);
                        // 直接清理本地状态
                        cleanupUploader();
                    }
                };

                // 将重置函数暴露为全局函数，供图片恢复时使用
                window[`resetUploader${index}`] = resetUploader;

                // 清理上传器状态
                const cleanupUploader = () => {
                    // 清除文件输入的值
                    fileInput.value = '';
                    // 清除dataset中的图片信息
                    delete fileInput.dataset.imageUrl;
                    delete fileInput.dataset.imageFilename;
                    // 显示上传容器
                    uploadContainer.style.display = 'flex';
                    // 清空预览区域
                    previewContainer.innerHTML = '';
                    previewContainer.style.height = '0';
                    // 重置状态
                    hasUploadedFile = false;

                    // 保存商品数据
                    saveProductData();
                };
                
                // 点击上传区域触发文件选择
                uploadContainer.addEventListener('click', () => {
                    fileInput.click();
                });
                
                // 拖拽上传
                uploadContainer.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadContainer.style.borderColor = '#0c4da2';
                });
                
                uploadContainer.addEventListener('dragleave', () => {
                    uploadContainer.style.borderColor = '#ddd';
                });
                
                uploadContainer.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadContainer.style.borderColor = '#ddd';
                    
                    if (e.dataTransfer.files.length) {
                        handleUploadedFile(e.dataTransfer.files[0]);
                    }
                });
                
                // 文件选择变化时处理图片
                fileInput.addEventListener('change', (e) => {
                    if (e.target.files && e.target.files[0]) {
                        handleUploadedFile(e.target.files[0]);
                    }
                });
                
                // 处理上传的文件
                const handleUploadedFile = (file) => {
                    // 检查文件类型
                    if (!file.type.match('image.*')) {
                        showToast('请上传图片文件', 'error', '格式错误');
                        resetUploader();
                        return;
                    }

                    // 检查文件大小（放宽限制，因为会自动压缩）
                    if (file.size > 10 * 1024 * 1024) {
                        showToast('图片大小不能超过10MB', 'error', '文件过大');
                        resetUploader();
                        return;
                    }

                    // 显示上传进度
                    previewContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #007bff;">正在处理图片...</div>';

                    // 使用图片压缩功能
                    compressImage(file, (compressedFile) => {
                        // 上传压缩后的图片到服务器
                        uploadImageToServer(compressedFile, index, (success, result) => {
                            if (success) {
                                // 清空预览区域
                                previewContainer.innerHTML = '';

                                // 隐藏上传区域，显示预览
                                uploadContainer.style.display = 'none';

                                const previewItem = document.createElement('div');
                                previewItem.className = 'preview-item';

                                const img = document.createElement('img');
                                img.src = result.fileUrl;

                                // 确保图片加载完成后显示完整
                                img.onload = function() {
                                    previewContainer.style.height = 'auto';
                                };

                                const removeBtn = document.createElement('div');
                                removeBtn.className = 'preview-remove';
                                removeBtn.innerHTML = '<i class="fas fa-times"></i>';

                                // 点击删除按钮时重置上传区域（会自动删除服务器文件）
                                removeBtn.addEventListener('click', (e) => {
                                    e.stopPropagation();
                                    resetUploader();
                                });

                                previewItem.appendChild(img);
                                previewItem.appendChild(removeBtn);
                                previewContainer.appendChild(previewItem);

                                // 保存文件信息到input的dataset中
                                fileInput.dataset.imageUrl = result.fileUrl;
                                fileInput.dataset.imageFilename = result.filename;

                                // 更新状态
                                hasUploadedFile = true;

                                showToast(`图片${index}上传成功`, 'success');

                                // 保存商品数据
                                saveProductData();
                            } else {
                                showToast('图片上传失败：' + result.message, 'error');
                                resetUploader();
                            }
                        });
                    }, index);
                };
            };
            
            // 表单提交处理
            const handleFormSubmit = (e) => {
                e.preventDefault();

                // 获取提交按钮引用
                const submitBtn = document.getElementById('submitBtn');
                const originalText = submitBtn.textContent;

                // 获取表单数据
                const form = document.getElementById('productForm');
                const formData = new FormData(form);

                // 收集已上传的图片信息
                const uploadedImages = [];
                for (let i = 1; i <= 5; i++) {
                    const fileInput = form.querySelector(`input[name="productImage${i}"]`);
                    if (fileInput && fileInput.dataset.imageUrl) {
                        uploadedImages.push({
                            index: i,
                            url: fileInput.dataset.imageUrl,
                            filename: fileInput.dataset.imageFilename
                        });
                    }
                }

                // 获取管理员登录信息
                const adminData = JSON.parse(sessionStorage.getItem('loggedInAdmin') || '{}');

                if (!adminData.username) {
                    showToast('未找到管理员登录信息', 'error', '验证失败');
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                    return;
                }

                // 收集商品类型数据
                const productTypes = [];
                const typeNameInputs = form.querySelectorAll('input[name="productTypeName[]"]');
                const typePriceInputs = form.querySelectorAll('input[name="productTypePrice[]"]');

                for (let i = 0; i < typeNameInputs.length; i++) {
                    const typeName = typeNameInputs[i].value.trim();
                    const typePrice = parseFloat(typePriceInputs[i].value);

                    if (typeName && !isNaN(typePrice) && typePrice >= 0) {
                        productTypes.push({
                            name: typeName,
                            price: typePrice
                        });
                    }
                }

                // 构建商品数据对象
                const productData = {
                    name: formData.get('productName'),
                    types: productTypes, // 使用商品类型替代单一价格
                    stock: parseInt(formData.get('productStock')),
                    description: formData.get('productDescription'),
                    keywords: selectedKeywords,
                    status: 'active', // 默认上架状态
                    images: uploadedImages,
                    createdBy: adminData.username, // 添加创建者信息
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };

                // 验证必填字段
                if (!productData.name || productData.types.length === 0 || productData.stock < 0) {
                    showToast('请填写完整的商品信息，至少需要一个商品类型', 'error', '验证失败');
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                    return;
                }

                // 检查是否为编辑模式
                const isEditMode = window.editingProductId;

                // 显示提交进度
                submitBtn.textContent = isEditMode ? '正在更新...' : '正在发布...';
                submitBtn.disabled = true;

                // 根据模式选择API端点和方法
                const apiUrl = isEditMode ? `/api/products/${window.editingProductId}` : '/api/products';
                const method = isEditMode ? 'PUT' : 'POST';

                // 如果是编辑模式，添加商品ID
                if (isEditMode) {
                    productData.id = window.editingProductId;
                }

                // 调试信息
                console.log('发送商品数据:', JSON.stringify(productData, null, 2));
                console.log('API URL:', apiUrl);
                console.log('HTTP方法:', method);

                // 发送到服务器保存
                fetch(apiUrl, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(productData)
                })
                .then(response => {
                    console.log('响应状态:', response.status);
                    console.log('响应头:', response.headers);
                    return response.text(); // 先获取文本，然后尝试解析JSON
                })
                .then(responseText => {
                    console.log('响应文本:', responseText);
                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (e) {
                        throw new Error('服务器返回的不是有效的JSON: ' + responseText);
                    }
                    return data;
                })
                .then(data => {
                    if (data.success) {
                        const successMessage = isEditMode ? '商品已成功更新' : '商品已成功发布';
                        showToast(successMessage, 'success', '操作成功');

                        // 清理保存的商品数据
                        fetch('/api/delete-product-data', {
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                username: adminData.username
                            })
                        })
                        .catch(error => {
                            console.warn('清理保存数据失败:', error);
                        });

                        // 重置表单，清除所有数据
                        resetFormCompletely();

                        // 清除编辑模式标记
                        delete window.editingProductId;

                        // 重定向到商品管理页面
                        setTimeout(() => {
                            window.location.href = 'admin-dashboard.html';
                        }, 1500);
                    } else {
                        throw new Error(data.message || '发布失败');
                    }
                })
                .catch(error => {
                    console.error('商品发布失败:', error);
                    console.error('错误详情:', error.stack);

                    let errorMessage = '商品发布失败：' + error.message;
                    if (error.message.includes('Unexpected token')) {
                        errorMessage = '服务器响应格式错误，请检查服务器日志';
                    }

                    showToast(errorMessage, 'error', '操作失败');

                    // 恢复按钮状态
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                });
            };

            // 加载商品数据用于编辑
            const loadProductForEdit = (productId) => {
                console.log('加载商品用于编辑:', productId);

                // 从服务器获取商品数据
                fetch(`/api/products/${productId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.product) {
                        const product = data.product;
                        console.log('加载的商品数据:', product);

                        // 填充表单字段
                        const form = document.getElementById('productForm');
                        if (form) {
                            const nameInput = form.querySelector('input[name="productName"]');
                            const stockInput = form.querySelector('input[name="productStock"]');
                            const descInput = form.querySelector('textarea[name="productDescription"]');

                            if (nameInput) nameInput.value = product.name || '';
                            if (stockInput) stockInput.value = product.stock || '';
                            if (descInput) descInput.value = product.description || '';
                        }

                        // 加载商品类型数据
                        if (product.types && Array.isArray(product.types)) {
                            loadProductTypes(product.types);
                        } else if (product.price !== undefined) {
                            // 兼容旧数据格式，将单一价格转换为商品类型
                            loadProductTypes([{ name: '默认类型', price: product.price }]);
                        }

                        // 设置关键词
                        if (product.keywords && Array.isArray(product.keywords)) {
                            selectedKeywords = product.keywords;
                            updateSelectedKeywordsDisplay();
                        }

                        // 加载图片
                        console.log('商品图片数据:', product.images);
                        if (product.images && Array.isArray(product.images)) {
                            console.log(`找到 ${product.images.length} 张图片`);
                            product.images.forEach((image, idx) => {
                                console.log(`处理图片 ${idx + 1}:`, image);
                                if (image.index && image.url) {
                                    loadImageForEdit(image.index, image.url, image.filename);
                                } else {
                                    console.warn('图片数据不完整:', image);
                                }
                            });
                        } else {
                            console.log('没有找到图片数据或图片数据格式不正确');
                        }

                        // 保存商品ID用于更新
                        window.editingProductId = productId;

                        showToast('商品数据加载成功', 'success');
                    } else {
                        showToast('加载商品数据失败: ' + (data.message || '未知错误'), 'error');
                    }
                })
                .catch(error => {
                    console.error('加载商品数据失败:', error);
                    showToast('加载商品数据失败: ' + error.message, 'error');
                });
            };

            // 为编辑模式加载图片
            const loadImageForEdit = (index, imageUrl, filename) => {
                console.log(`加载图片 ${index}:`, imageUrl, filename);

                // 使用正确的选择器获取元素
                const uploadItem = document.getElementById(`imageUploadItem${index}`);
                if (!uploadItem) {
                    console.error(`未找到图片上传项: imageUploadItem${index}`);
                    return;
                }

                const uploadContainer = uploadItem.querySelector('.image-upload-container');
                const previewContainer = document.getElementById(`imagePreview${index}`);
                const fileInput = uploadItem.querySelector('.product-image-input');

                if (!uploadContainer || !previewContainer || !fileInput) {
                    console.error('未找到必要的图片上传元素:', {
                        uploadContainer: !!uploadContainer,
                        previewContainer: !!previewContainer,
                        fileInput: !!fileInput
                    });
                    return;
                }

                // 隐藏上传区域
                uploadContainer.style.display = 'none';

                // 清空预览容器
                previewContainer.innerHTML = '';

                // 创建预览元素
                const previewDiv = document.createElement('div');
                previewDiv.className = 'image-preview-item';
                previewDiv.style.position = 'relative';
                previewDiv.style.display = 'inline-block';
                previewDiv.style.width = '100%';

                const img = document.createElement('img');

                // 确保图片URL是完整的
                let fullImageUrl = imageUrl;
                if (imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('/')) {
                    fullImageUrl = '/uploads/' + imageUrl;
                } else if (imageUrl && imageUrl.startsWith('/uploads/')) {
                    fullImageUrl = imageUrl;
                } else if (imageUrl && !imageUrl.startsWith('http')) {
                    fullImageUrl = '/' + imageUrl.replace(/^\/+/, '');
                }

                console.log('原始图片URL:', imageUrl);
                console.log('完整图片URL:', fullImageUrl);

                img.src = fullImageUrl;
                img.style.width = '100%';
                img.style.height = 'auto';
                img.style.maxHeight = '200px';
                img.style.objectFit = 'cover';
                img.style.borderRadius = '5px';

                // 添加图片加载错误处理
                img.onerror = function() {
                    console.error('图片加载失败:', fullImageUrl);
                    console.error('尝试的URL:', this.src);
                    previewDiv.innerHTML = `
                        <div style="padding: 20px; text-align: center; color: #999; border: 1px dashed #ddd; border-radius: 5px;">
                            <i class="fas fa-exclamation-triangle"></i><br>
                            图片加载失败<br>
                            <small>${fullImageUrl}</small>
                        </div>
                    `;
                };

                img.onload = function() {
                    console.log('图片加载成功:', fullImageUrl);
                };

                const removeBtn = document.createElement('div');
                removeBtn.className = 'image-remove-btn';
                removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                removeBtn.style.position = 'absolute';
                removeBtn.style.top = '5px';
                removeBtn.style.right = '5px';
                removeBtn.style.width = '25px';
                removeBtn.style.height = '25px';
                removeBtn.style.backgroundColor = 'rgba(255, 0, 0, 0.8)';
                removeBtn.style.color = 'white';
                removeBtn.style.borderRadius = '50%';
                removeBtn.style.display = 'flex';
                removeBtn.style.alignItems = 'center';
                removeBtn.style.justifyContent = 'center';
                removeBtn.style.cursor = 'pointer';
                removeBtn.style.fontSize = '12px';

                removeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    // 重置该图片上传区域
                    uploadContainer.style.display = 'block';
                    previewContainer.innerHTML = '';
                    fileInput.value = '';
                    delete fileInput.dataset.imageUrl;
                    delete fileInput.dataset.imageFilename;
                    delete fileInput.dataset.isExisting;
                    console.log(`图片 ${index} 已移除`);
                });

                previewDiv.appendChild(img);
                previewDiv.appendChild(removeBtn);
                previewContainer.appendChild(previewDiv);

                // 保存图片信息到文件输入元素
                fileInput.dataset.imageUrl = fullImageUrl;
                fileInput.dataset.imageFilename = filename || '';

                // 标记这是一个已存在的图片（不是新上传的）
                fileInput.dataset.isExisting = 'true';

                console.log(`图片 ${index} 预览已设置`);
            };

            // 完全重置表单
            const resetFormCompletely = () => {
                try {
                    // 重置表单字段
                    const form = document.getElementById('productForm');
                    form.reset();

                    // 清空已选择的关键词
                    selectedKeywords = [];
                    updateSelectedKeywordsDisplay();

                    // 重置所有图片上传区域
                    for (let i = 1; i <= 5; i++) {
                        const uploadItem = document.getElementById(`imageUploadItem${i}`);
                        if (uploadItem) {
                            const uploadContainer = uploadItem.querySelector('.image-upload-container');
                            const previewContainer = document.getElementById(`imagePreview${i}`);
                            const fileInput = uploadItem.querySelector('.product-image-input');

                            // 显示上传区域，清空预览
                            if (uploadContainer) uploadContainer.style.display = 'block';
                            if (previewContainer) {
                                previewContainer.innerHTML = '';
                            }

                            // 清除文件输入和数据
                            if (fileInput) {
                                fileInput.value = '';
                                delete fileInput.dataset.imageUrl;
                                delete fileInput.dataset.imageFilename;
                            }
                        }
                    }

                    // 清除编辑模式标记
                    delete window.editingProductId;

                    console.log('表单已完全重置');
                } catch (error) {
                    console.error('重置表单失败:', error);
                }
            };

            // 显示通知提示
            const showToast = (message, type = 'info', title = '') => {
                const toastContainer = document.getElementById('toastContainer');
                
                const toast = document.createElement('div');
                toast.className = `toast toast-${type}`;
                
                let iconClass;
                switch(type) {
                    case 'success':
                        iconClass = 'fas fa-check-circle';
                        break;
                    case 'error':
                        iconClass = 'fas fa-exclamation-circle';
                        break;
                    case 'warning':
                        iconClass = 'fas fa-exclamation-triangle';
                        break;
                    default:
                        iconClass = 'fas fa-info-circle';
                }
                
                toast.innerHTML = `
                    <div class="toast-icon">
                        <i class="${iconClass}"></i>
                    </div>
                    <div class="toast-content">
                        ${title ? `<div class="toast-title">${title}</div>` : ''}
                        <div class="toast-message">${message}</div>
                    </div>
                `;
                
                toastContainer.appendChild(toast);
                
                // 3秒后移除通知
                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(100%)';
                    
                    setTimeout(() => {
                        toast.remove();
                    }, 300);
                }, 3000);
            };
            
            // 图片压缩函数（参考web_order.html的实现）
            const compressImage = (file, callback, imageIndex) => {
                // 创建图像对象
                const img = new Image();
                const reader = new FileReader();

                reader.onload = function(e) {
                    img.src = e.target.result;

                    img.onload = function() {
                        // 创建Canvas
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // 计算压缩后的尺寸
                        let width = img.width;
                        let height = img.height;
                        const maxDimension = 1200; // 最大尺寸

                        // 如果图片尺寸超出了最大限制
                        if (width > maxDimension || height > maxDimension) {
                            if (width > height) {
                                height = Math.round(height * maxDimension / width);
                                width = maxDimension;
                            } else {
                                width = Math.round(width * maxDimension / height);
                                height = maxDimension;
                            }
                        }

                        // 设置Canvas尺寸
                        canvas.width = width;
                        canvas.height = height;

                        // 绘制到Canvas
                        ctx.drawImage(img, 0, 0, width, height);

                        // 根据文件大小确定压缩质量
                        let quality = 0.7; // 默认质量70%

                        if (file.size > 5 * 1024 * 1024) { // 大于5MB
                            quality = 0.5;
                        } else if (file.size > 2 * 1024 * 1024) { // 大于2MB
                            quality = 0.6;
                        } else if (file.size > 1 * 1024 * 1024) { // 大于1MB
                            quality = 0.7;
                        } else {
                            // 对于1MB以下的图片也进行适度压缩
                            quality = 0.8;
                        }

                        // 将Canvas导出为Blob
                        canvas.toBlob(
                            function(blob) {
                                // 创建新的文件对象
                                const compressedFile = new File(
                                    [blob],
                                    file.name,
                                    {
                                        type: 'image/jpeg',
                                        lastModified: Date.now()
                                    }
                                );

                                console.log(`商品图片${imageIndex}压缩: ${Math.round(file.size/1024)}KB → ${Math.round(blob.size/1024)}KB (${Math.round((1 - blob.size/file.size) * 100)}% 压缩率)`);

                                // 返回压缩后的文件
                                callback(compressedFile);
                            },
                            'image/jpeg',
                            quality
                        );
                    };
                };

                reader.readAsDataURL(file);
            };

            // 上传图片到服务器
            const uploadImageToServer = (file, imageIndex, callback) => {
                // 获取管理员登录信息
                const adminData = JSON.parse(sessionStorage.getItem('loggedInAdmin') || '{}');

                if (!adminData.username) {
                    callback(false, { message: '未找到管理员登录信息' });
                    return;
                }

                const formData = new FormData();
                formData.append('image', file);
                formData.append('type', 'product');
                formData.append('imageIndex', imageIndex);
                formData.append('username', adminData.username); // 添加用户名

                // 发送上传请求到服务器
                fetch('/api/upload-image', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('商品图片上传成功:', data.filename);
                        callback(true, data);
                    } else {
                        console.error('商品图片上传失败:', data.message);
                        callback(false, data);
                    }
                })
                .catch(error => {
                    console.error('商品图片上传错误:', error);
                    callback(false, { message: '网络错误，请稍后重试' });
                });
            };

            // 从服务器删除图片（参考web_order.html的删除逻辑）
            const deleteImageFromServer = (filename, callback) => {
                if (!filename) {
                    console.log('删除图片：文件名为空，跳过删除');
                    callback();
                    return;
                }

                // 获取管理员登录信息
                const adminData = JSON.parse(sessionStorage.getItem('loggedInAdmin') || '{}');

                if (!adminData.username) {
                    console.error('未找到管理员登录信息，无法删除图片');
                    callback();
                    return;
                }

                console.log('正在删除图片:', filename, '用户:', adminData.username);

                // 使用DELETE方法和URL路径参数（参考web_order.html）
                fetch(`/api/delete-image/${filename}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: adminData.username
                    })
                })
                .then(response => {
                    console.log('删除API响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('删除API响应数据:', data);
                    if (data.success) {
                        console.log('✅ 图片删除成功:', filename);
                    } else {
                        console.error('❌ 图片删除失败:', data.message);
                    }
                    callback();
                })
                .catch(error => {
                    console.error('❌ 图片删除网络错误:', error);
                    callback();
                });
            };



            // 清理所有已上传的图片（参考web_order.html的批量删除逻辑）
            const cleanupAllUploadedImages = (callback) => {
                // 获取管理员登录信息
                const adminData = JSON.parse(sessionStorage.getItem('loggedInAdmin') || '{}');

                if (!adminData.username) {
                    console.warn('未找到管理员登录信息，无法删除图片');
                    callback();
                    return;
                }

                const form = document.getElementById('productForm');
                const filesToDelete = [];

                // 收集所有已上传的图片文件名
                for (let i = 1; i <= 5; i++) {
                    const fileInput = form.querySelector(`input[name="productImage${i}"]`);
                    if (fileInput && fileInput.dataset.imageFilename) {
                        filesToDelete.push(fileInput.dataset.imageFilename);
                    }
                }

                if (filesToDelete.length === 0) {
                    console.log('没有需要删除的图片');
                    callback();
                    return;
                }

                console.log('准备批量删除图片:', filesToDelete);

                // 使用批量删除API（参考web_order.html）
                fetch('/api/delete-multiple-images', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: adminData.username,
                        filenames: filesToDelete
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log(`✅ 成功删除 ${data.deletedCount} 张图片`);
                    } else {
                        console.warn('❌ 批量删除图片失败:', data.message);
                    }
                    callback();
                })
                .catch(error => {
                    console.error('❌ 批量删除图片错误:', error);
                    callback();
                });
            };

            // 注意：不在页面卸载时删除图片，只有用户主动点击删除按钮时才删除
            // 这样确保刷新页面时图片不会被误删除

            // 商品类型管理函数
            let productTypeIndex = 1; // 从1开始，因为第一个是0

            // 保存商品类型到服务器
            function saveProductTypes() {
                const productId = getUrlParameter('id');
                if (!productId) {
                    return; // 新商品不需要实时保存
                }

                const types = collectProductTypes();
                if (types.length === 0) {
                    return;
                }

                // 构建更新数据
                const updateData = {
                    types: types
                };

                fetch(`/api/products/${productId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        console.error('保存商品类型失败:', data.message);
                    }
                })
                .catch(error => {
                    console.error('保存商品类型时发生错误:', error);
                });
            }

            // 收集当前的商品类型数据
            function collectProductTypes() {
                const container = document.getElementById('productTypesContainer');
                const typeItems = container.querySelectorAll('.product-type-item');
                const types = [];

                typeItems.forEach(item => {
                    const nameInput = item.querySelector('.type-name-input');
                    const priceInput = item.querySelector('.type-price-input');

                    if (nameInput && priceInput && nameInput.value.trim() && priceInput.value) {
                        types.push({
                            name: nameInput.value.trim(),
                            price: parseFloat(priceInput.value)
                        });
                    }
                });

                return types;
            }

            // 添加商品类型
            window.addProductType = function() {
                const container = document.getElementById('productTypesContainer');
                const currentTypes = container.querySelectorAll('.product-type-item').length;

                if (currentTypes >= 10) {
                    showToast('最多只能添加10个商品类型', 'warning', '提示');
                    return;
                }

                const newTypeHtml = `
                    <div class="product-type-item" data-index="${productTypeIndex}">
                        <div class="type-input-group">
                            <input type="text" name="productTypeName[]" class="form-control type-name-input" placeholder="请输入商品类型名称" required oninput="saveProductTypes()">
                            <input type="number" name="productTypePrice[]" class="form-control type-price-input" placeholder="0.00" step="0.01" min="0" required oninput="saveProductTypes()">
                            <button type="button" class="btn btn-danger remove-type-btn" onclick="removeProductType(${productTypeIndex})">删除</button>
                        </div>
                    </div>
                `;

                container.insertAdjacentHTML('beforeend', newTypeHtml);
                productTypeIndex++;

                // 更新删除按钮显示状态
                updateRemoveButtonsVisibility();

                // 更新添加按钮状态
                updateAddButtonState();
            };

            // 删除商品类型
            window.removeProductType = function(index) {
                const typeItem = document.querySelector(`[data-index="${index}"]`);
                if (typeItem) {
                    typeItem.remove();
                    updateRemoveButtonsVisibility();
                    updateAddButtonState();
                    saveProductTypes(); // 删除后保存
                }
            };

            // 更新删除按钮显示状态
            function updateRemoveButtonsVisibility() {
                const container = document.getElementById('productTypesContainer');
                const typeItems = container.querySelectorAll('.product-type-item');

                typeItems.forEach((item, index) => {
                    const removeBtn = item.querySelector('.remove-type-btn');
                    if (typeItems.length > 1) {
                        removeBtn.style.display = 'block';
                    } else {
                        removeBtn.style.display = 'none';
                    }
                });
            }

            // 更新添加按钮状态
            function updateAddButtonState() {
                const container = document.getElementById('productTypesContainer');
                const currentTypes = container.querySelectorAll('.product-type-item').length;
                const addBtn = document.getElementById('addTypeBtn');

                if (currentTypes >= 10) {
                    addBtn.disabled = true;
                    addBtn.textContent = '已达到最大数量 (10个)';
                } else {
                    addBtn.disabled = false;
                    addBtn.innerHTML = '<i class="fas fa-plus"></i> 添加商品类型';
                }
            }

            // 加载商品类型数据
            function loadProductTypes(types) {
                const container = document.getElementById('productTypesContainer');

                // 清空现有内容
                container.innerHTML = '';

                // 重置索引
                productTypeIndex = 0;

                // 加载每个商品类型
                types.forEach((type, index) => {
                    const typeHtml = `
                        <div class="product-type-item" data-index="${index}">
                            <div class="type-input-group">
                                <input type="text" name="productTypeName[]" class="form-control type-name-input" placeholder="请输入商品类型名称" value="${type.name || ''}" required oninput="saveProductTypes()">
                                <input type="number" name="productTypePrice[]" class="form-control type-price-input" placeholder="0.00" step="0.01" min="0" value="${type.price || ''}" required oninput="saveProductTypes()">
                                <button type="button" class="btn btn-danger remove-type-btn" onclick="removeProductType(${index})" style="${types.length > 1 ? '' : 'display: none;'}">删除</button>
                            </div>
                        </div>
                    `;
                    container.insertAdjacentHTML('beforeend', typeHtml);
                });

                // 更新索引为下一个可用值
                productTypeIndex = types.length;

                // 更新按钮状态
                updateAddButtonState();
            }

            // 初始化页面
            initPage();

            // 初始化商品类型按钮状态
            updateRemoveButtonsVisibility();
            updateAddButtonState();
        });
    </script>
</body>
</html>