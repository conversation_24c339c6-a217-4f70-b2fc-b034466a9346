<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品详情 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="js/blacklist-handler.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 0;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: #333;
        }

        .logo-img {
            width: 40px;
            height: 40px;
            margin-right: 10px;
            border-radius: 8px;
        }

        .logo-text {
            font-size: 20px;
            font-weight: bold;
        }

        .nav-buttons {
            display: flex;
            gap: 15px;
        }

        .nav-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-back {
            background: #3498db;
            color: white;
        }

        .btn-back:hover {
            background: #2980b9;
        }

        .btn-home {
            background: #2ecc71;
            color: white;
        }

        .btn-home:hover {
            background: #27ae60;
        }

        /* Main Content */
        main {
            padding: 30px 0;
            min-height: calc(100vh - 100px);
        }

        .product-detail-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .product-detail-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            padding: 40px;
        }

        /* Product Images */
        .product-images {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .main-image {
            width: 100%;
            height: 400px;
            border-radius: 12px;
            overflow: hidden;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .main-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .thumbnail-images {
            display: flex;
            gap: 10px;
            overflow-x: auto;
        }

        .thumbnail {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .thumbnail.active {
            border-color: #3498db;
        }

        .thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Product Info */
        .product-info {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .product-title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            line-height: 1.3;
        }

        .product-price {
            display: flex;
            align-items: baseline;
            gap: 15px;
        }

        .current-price {
            font-size: 32px;
            font-weight: bold;
            color: #e74c3c;
        }

        .original-price {
            font-size: 18px;
            color: #999;
            text-decoration: line-through;
        }

        .product-meta {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .meta-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .meta-label {
            font-weight: bold;
            color: #666;
        }

        .meta-value {
            color: #333;
        }

        .product-description {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .description-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .description-content {
            color: #666;
            line-height: 1.6;
        }

        .product-actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .action-btn {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-add-cart {
            background: #f39c12;
            color: white;
        }

        .btn-add-cart:hover {
            background: #e67e22;
        }

        .btn-buy-now {
            background: #e74c3c;
            color: white;
        }

        .btn-buy-now:hover {
            background: #c0392b;
        }

        /* Loading State */
        .loading {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .loading i {
            font-size: 48px;
            margin-bottom: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Error State */
        .error {
            text-align: center;
            padding: 60px 20px;
            color: #e74c3c;
        }

        .error i {
            font-size: 48px;
            margin-bottom: 20px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .product-detail-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }

            .product-title {
                font-size: 24px;
            }

            .current-price {
                font-size: 28px;
            }

            .product-meta {
                grid-template-columns: 1fr;
            }

            .product-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">
                    <img src="images/logo.png" alt="金舟国际物流" class="logo-img" onerror="this.style.display='none'">
                    <span class="logo-text">金舟国际物流</span>
                </a>
                <div class="nav-buttons">
                    <button class="nav-btn btn-back" onclick="goBack()">
                        <i class="fas fa-arrow-left"></i> 返回
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <div class="container">
            <div class="product-detail-container">
                <div id="productDetailContent">
                    <!-- 商品详情内容将通过JavaScript动态加载 -->
                    <div class="loading">
                        <i class="fas fa-spinner"></i>
                        <h3>正在加载商品详情...</h3>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 加载商品详情
        function loadProductDetail() {
            const productId = getUrlParameter('id');
            
            if (!productId) {
                showError('商品ID不存在');
                return;
            }

            // 从服务器获取商品详情
            fetch(`/api/products/${productId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.product) {
                    renderProductDetail(data.product);
                } else {
                    showError('商品不存在或已下架');
                }
            })
            .catch(error => {
                console.error('加载商品详情失败:', error);
                showError('网络错误，请稍后重试');
            });
        }

        // 渲染商品详情
        function renderProductDetail(product) {
            const container = document.getElementById('productDetailContent');

            // 处理图片 - 优先使用 mainImage，然后是 images 数组
            let images = [];
            let mainImage = '';

            if (product.mainImage) {
                // 如果有主图，使用主图作为第一张图片
                mainImage = product.mainImage;
                images = [{ url: product.mainImage }];

                // 如果还有其他图片，添加到数组中
                if (product.images && product.images.length > 0) {
                    const additionalImages = product.images.filter(img => img.url !== product.mainImage);
                    images = images.concat(additionalImages);
                }
            } else if (product.images && product.images.length > 0) {
                // 如果没有主图但有图片数组，使用第一张作为主图
                images = product.images;
                mainImage = images[0].url;
            } else {
                // 如果都没有，使用占位图
                mainImage = 'https://via.placeholder.com/400x400/f0f0f0/999999?text=暂无图片';
                images = [];
            }

            // 生成缩略图
            const thumbnailsHtml = images.map((img, index) => `
                <div class="thumbnail ${index === 0 ? 'active' : ''}" onclick="changeMainImage('${img.url}', this)">
                    <img src="${img.url}" alt="商品图片${index + 1}" onerror="this.parentElement.style.display='none'">
                </div>
            `).join('');

            container.innerHTML = `
                <div class="product-detail-content">
                    <div class="product-images">
                        <div class="main-image">
                            <img id="mainProductImage" src="${mainImage}" alt="${product.name}" onerror="this.src='https://via.placeholder.com/400x400/f0f0f0/999999?text=暂无图片'">
                        </div>
                        ${images.length > 1 ? `<div class="thumbnail-images">${thumbnailsHtml}</div>` : ''}
                    </div>
                    <div class="product-info">
                        <h1 class="product-title">${product.name}</h1>
                        <div class="product-price">
                            <span class="current-price">¥${parseFloat(product.price).toFixed(2)}</span>
                            <span class="original-price">¥${(parseFloat(product.price) * 1.3).toFixed(2)}</span>
                        </div>
                        <div class="product-meta">
                            <div class="meta-item">
                                <span class="meta-label">商品ID:</span>
                                <span class="meta-value">${product.id}</span>
                            </div>
                            <div class="meta-item">
                                <span class="meta-label">库存:</span>
                                <span class="meta-value">${product.stock || '有库存'}</span>
                            </div>
                            <div class="meta-item">
                                <span class="meta-label">状态:</span>
                                <span class="meta-value">${product.status === 'active' ? '上架中' : '下架'}</span>
                            </div>
                            <div class="meta-item">
                                <span class="meta-label">发布时间:</span>
                                <span class="meta-value">${new Date(product.publishedAt).toLocaleDateString('zh-CN')}</span>
                            </div>
                        </div>
                        ${product.description ? `
                        <div class="product-description">
                            <div class="description-title">商品描述</div>
                            <div class="description-content">${product.description}</div>
                        </div>
                        ` : ''}
                        <div class="product-actions">
                            <button class="action-btn btn-add-cart" onclick="addToCart('${product.id}')">
                                <i class="fas fa-shopping-cart"></i> 加入购物车
                            </button>
                            <button class="action-btn btn-buy-now" onclick="buyNow('${product.id}')">
                                <i class="fas fa-bolt"></i> 立即购买
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // 更新页面标题
            document.title = `${product.name} - 金舟国际物流`;
        }

        // 显示错误信息
        function showError(message) {
            const container = document.getElementById('productDetailContent');
            container.innerHTML = `
                <div class="error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>${message}</h3>
                    <button onclick="location.reload()" style="margin-top: 20px; background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">重新加载</button>
                </div>
            `;
        }

        // 切换主图
        function changeMainImage(imageUrl, thumbnail) {
            const mainImage = document.getElementById('mainProductImage');
            mainImage.src = imageUrl;
            
            // 更新缩略图激活状态
            document.querySelectorAll('.thumbnail').forEach(thumb => thumb.classList.remove('active'));
            thumbnail.classList.add('active');
        }

        // 加入购物车
        function addToCart(productId) {
            alert(`商品 ${productId} 已加入购物车！`);
        }

        // 立即购买
        function buyNow(productId) {
            alert(`即将跳转到购买页面，商品ID: ${productId}`);
        }

        // 智能返回函数
        function goBack() {
            // 检查是否有历史记录可以返回
            if (window.history.length > 1) {
                // 尝试使用 history.back()
                window.history.back();

                // 设置一个超时检查，如果页面没有变化，则使用备用方案
                setTimeout(() => {
                    // 如果页面仍然是当前页面，说明 history.back() 没有生效
                    if (window.location.href.includes('product-detail.html')) {
                        handleBackupReturn();
                    }
                }, 100);
            } else {
                // 没有历史记录，直接使用备用方案
                handleBackupReturn();
            }
        }

        // 备用返回方案
        function handleBackupReturn() {
            // 首先检查 URL 参数中的来源信息
            const urlParams = new URLSearchParams(window.location.search);
            const from = urlParams.get('from');

            if (from) {
                switch (from) {
                    case 'admin':
                        window.location.href = 'admin-dashboard.html';
                        return;
                    case 'recommend':
                        // 检查是否有搜索参数需要保留
                        const returnUrl = buildReturnUrl();
                        window.location.href = returnUrl;
                        return;
                    case 'carousel':
                        window.location.href = 'recommend.html'; // 轮播图商品页面返回推荐页面
                        return;
                    case 'hotsales':
                        window.location.href = 'hot-sales.html';
                        return;
                    default:
                        window.location.href = 'recommend.html';
                        return;
                }
            }

            // 如果没有 from 参数，检查 referrer 来判断来源
            const referrer = document.referrer;

            if (referrer) {
                if (referrer.includes('admin-dashboard.html')) {
                    // 来自管理员页面
                    window.location.href = 'admin-dashboard.html';
                } else if (referrer.includes('recommend.html')) {
                    // 来自推荐页面，尝试重建原始URL
                    const returnUrl = buildReturnUrlFromReferrer(referrer);
                    window.location.href = returnUrl;
                } else if (referrer.includes('hot-sales.html')) {
                    // 来自热销页面
                    window.location.href = 'hot-sales.html';
                } else if (referrer.includes('carousel-products.html')) {
                    // 来自轮播图商品页面
                    window.location.href = 'recommend.html';
                } else if (referrer.includes('index.html')) {
                    // 来自首页
                    window.location.href = 'index.html';
                } else {
                    // 其他情况，返回到推荐页面
                    window.location.href = 'recommend.html';
                }
            } else {
                // 没有 referrer，默认返回推荐页面
                window.location.href = 'recommend.html';
            }
        }

        // 构建返回URL，保留搜索参数和页码
        function buildReturnUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const returnParams = new URLSearchParams();

            // 保留搜索相关的参数
            const searchParams = ['search', 'category', 'categoryName', 'keyword'];
            searchParams.forEach(param => {
                const value = urlParams.get(param);
                if (value) {
                    returnParams.set(param, value);
                }
            });

            // 保留页码参数
            const page = urlParams.get('page');
            if (page && page !== '1') {
                returnParams.set('returnPage', page);
            }

            // 构建返回URL
            const baseUrl = 'recommend.html';
            if (returnParams.toString()) {
                return `${baseUrl}?${returnParams.toString()}`;
            } else {
                return baseUrl;
            }
        }

        // 从referrer URL中提取参数并构建返回URL
        function buildReturnUrlFromReferrer(referrer) {
            try {
                const referrerUrl = new URL(referrer);
                const referrerParams = referrerUrl.searchParams;
                const returnParams = new URLSearchParams();

                // 保留搜索相关的参数
                const searchParams = ['search', 'category', 'categoryName', 'keyword'];
                searchParams.forEach(param => {
                    const value = referrerParams.get(param);
                    if (value) {
                        returnParams.set(param, value);
                    }
                });

                // 从当前URL获取页码参数（因为referrer可能没有页码信息）
                const currentParams = new URLSearchParams(window.location.search);
                const page = currentParams.get('page');
                if (page && page !== '1') {
                    returnParams.set('returnPage', page);
                }

                // 构建返回URL
                const baseUrl = 'recommend.html';
                if (returnParams.toString()) {
                    return `${baseUrl}?${returnParams.toString()}`;
                } else {
                    return baseUrl;
                }
            } catch (error) {
                console.error('解析referrer URL失败:', error);
                return 'recommend.html';
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadProductDetail();
        });
    </script>
</body>
</html>
