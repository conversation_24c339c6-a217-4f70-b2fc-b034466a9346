<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员控制台 - 金舟国际物流</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 引入Flatpickr日期选择器CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <!-- 引入Chart.js图表库 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background-color: #0c4da2;
            color: white;
            padding: 20px 0;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        }
        
        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .logo-img {
            width: 60px;
            height: 60px;
            margin-bottom: 10px;
            border-radius: 10px;
            background-color: white;
            padding: 5px;
        }
        
        .sidebar-title {
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .admin-badge {
            display: inline-block;
            background-color: #D4AF37;
            color: white;
            font-size: 12px;
            padding: 3px 10px;
            border-radius: 20px;
            margin-top: 5px;
        }
        
        .nav-menu {
            list-style: none;
        }
        
        .nav-item {
            margin-bottom: 5px;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .nav-link:hover, .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .nav-icon {
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            padding: 30px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .page-title {
            font-size: 24px;
            color: #0c4da2;
        }
        
        .header-actions {
            display: flex;
            align-items: center;
        }
        
        .admin-info {
            display: flex;
            align-items: center;
            margin-right: 20px;
        }
        
        .admin-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #0c4da2;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .admin-name {
            font-weight: bold;
        }
        
        .logout-btn {
            background-color: #f5f5f5;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
        }
        
        .logout-btn:hover {
            background-color: #e0e0e0;
        }
        
        .logout-icon {
            margin-right: 5px;
        }
        
        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        
        .stat-card {
            display: flex;
            flex-direction: column;
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .stat-title {
            color: #666;
            font-size: 16px;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(12, 77, 162, 0.1);
            color: #0c4da2;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #0c4da2;
        }
        
        .stat-change {
            margin-top: 10px;
            font-size: 14px;
            display: flex;
            align-items: center;
        }
        
        .stat-change.positive {
            color: #28a745;
        }
        
        .stat-change.negative {
            color: #dc3545;
        }
        
        .recent-activity {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .activity-title {
            font-size: 18px;
            color: #0c4da2;
            font-weight: bold;
        }
        
        .view-all {
            color: #0c4da2;
            text-decoration: none;
            font-size: 14px;
        }
        
        .activity-list {
            list-style: none;
        }
        
        .activity-item {
            display: flex;
            align-items: flex-start;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: rgba(12, 77, 162, 0.1);
            color: #0c4da2;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-text {
            margin-bottom: 5px;
        }
        
        .activity-time {
            font-size: 12px;
            color: #888;
        }

        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            max-width: 350px;
        }

        /* 商品表格样式 */
        .products-table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .products-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .products-table th {
            background: #f8f9fa;
            color: #333;
            font-weight: 600;
            padding: 12px 8px;
            text-align: left;
            border-bottom: 2px solid #e9ecef;
            white-space: nowrap;
        }



        .products-table td {
            padding: 12px 8px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }

        .products-table tr:hover {
            background-color: #f8f9fa;
        }

        .products-table tr:last-child td {
            border-bottom: none;
        }

        /* 商品图片 */
        .product-image-cell {
            text-align: center;
        }

        .product-image-thumb {
            width: 50px;
            height: 50px;
            border-radius: 6px;
            object-fit: cover;
            border: 1px solid #e9ecef;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .product-image-thumb:hover {
            transform: scale(1.1);
        }

        .product-image-placeholder {
            width: 50px;
            height: 50px;
            border-radius: 6px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            border: 1px solid #e9ecef;
        }

        /* 商品名称 */
        .product-name-cell {
            max-width: 200px;
        }

        .product-name-link {
            color: #1890ff;
            text-decoration: none;
            font-weight: 500;
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .product-name-link:hover {
            color: #40a9ff;
            text-decoration: underline;
        }

        .product-id {
            font-size: 12px;
            color: #999;
            margin-top: 2px;
        }

        /* 价格 */
        .product-price-cell {
            color: #f5222d;
            font-weight: 600;
            text-align: right;
        }

        /* 库存 */
        .product-stock-cell {
            text-align: center;
        }

        .stock-value {
            color: #52c41a;
            font-weight: 500;
        }

        .stock-low {
            color: #faad14;
        }

        .stock-out {
            color: #f5222d;
        }

        /* 销量 */
        .product-sales-cell {
            text-align: center;
            color: #666;
        }

        /* 时间 */
        .product-time-cell {
            color: #666;
            font-size: 12px;
        }

        /* 状态标签 */
        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .status-inactive {
            background: #fff2e8;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }

        /* 操作按钮 */
        .product-actions {
            display: flex;
            gap: 4px;
            justify-content: center;
        }

        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 2px;
        }

        .btn-edit {
            background: #1890ff;
            color: white;
        }

        .btn-edit:hover {
            background: #40a9ff;
        }

        .btn-delete {
            background: #ff4d4f;
            color: white;
        }

        .btn-delete:hover {
            background: #ff7875;
        }



        /* 响应式设计 */
        @media (max-width: 1200px) {
            .products-table th:nth-child(6),
            .products-table td:nth-child(6),
            .products-table th:nth-child(7),
            .products-table td:nth-child(7) {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .products-table th:nth-child(5),
            .products-table td:nth-child(5) {
                display: none;
            }

            .product-name-cell {
                max-width: 150px;
            }
        }
        
        .toast {
            display: flex;
            align-items: center;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            margin-bottom: 15px;
            padding: 12px 20px;
            overflow: hidden;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        .toast.toast-success {
            border-left: 5px solid #28a745;
        }
        
        .toast.toast-error {
            border-left: 5px solid #dc3545;
        }
        
        .toast.toast-info {
            border-left: 5px solid #17a2b8;
        }
        
        .toast.toast-warning {
            border-left: 5px solid #ffc107;
        }
        
        .toast-icon {
            margin-right: 15px;
            font-size: 20px;
        }
        
        .toast-success .toast-icon {
            color: #28a745;
        }
        
        .toast-error .toast-icon {
            color: #dc3545;
        }
        
        .toast-info .toast-icon {
            color: #17a2b8;
        }
        
        .toast-warning .toast-icon {
            color: #ffc107;
        }
        
        .toast-content {
            flex: 1;
        }
        
        .toast-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .toast-message {
            font-size: 14px;
            color: #666;
        }
        
        .toast-close {
            color: #999;
            font-size: 18px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .toast-close:hover {
            color: #333;
        }
        
        .toast-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background-color: rgba(0, 0, 0, 0.1);
            width: 100%;
        }
        
        .toast-progress-bar {
            height: 3px;
            width: 100%;
        }
        
        .toast-success .toast-progress-bar {
            background-color: #28a745;
        }
        
        .toast-error .toast-progress-bar {
            background-color: #dc3545;
        }
        
        .toast-info .toast-progress-bar {
            background-color: #17a2b8;
        }
        
        .toast-warning .toast-progress-bar {
            background-color: #ffc107;
        }

        .page-content {
            display: none;
        }
        
        .page-content.active {
            display: block;
        }
        
        .order-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .order-table th, 
        .order-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .order-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .order-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .user-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .user-table th, 
        .user-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .user-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .user-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .merchant-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .merchant-table th, 
        .merchant-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .merchant-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .merchant-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .status-pending {
            color: #ffc107;
            font-weight: 500;
        }
        
        .status-approved {
            color: #28a745;
            font-weight: 500;
        }
        
        .status-rejected {
            color: #dc3545;
            font-weight: 500;
        }
        
        /* 客服管理样式 */
        .section-title {
            margin: 25px 0 15px;
            font-size: 18px;
            color: #0c4da2;
        }
        
        .cs-table, .ticket-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .cs-table th, .cs-table td,
        .ticket-table th, .ticket-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .cs-table th, .ticket-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .cs-table tr:hover, .ticket-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .cs-stats {
            display: flex;
            gap: 20px;
            margin-top: 15px;
        }
        
        .cs-stats .stat-card {
            flex: 1;
            display: flex;
            align-items: center;
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .cs-stats .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background-color: rgba(12, 77, 162, 0.1);
            color: #0c4da2;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-right: 15px;
        }
        
        .cs-stats .stat-info {
            flex: 1;
        }
        
        .cs-stats .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #0c4da2;
        }
        
        .cs-stats .stat-title {
            font-size: 14px;
            color: #666;
        }
        
        .btn-add {
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 15px;
            cursor: pointer;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .btn-add:hover {
            background-color: #218838;
        }
        
        .status-active {
            color: #28a745;
            font-weight: 500;
        }
        
        .status-inactive {
            color: #6c757d;
            font-weight: 500;
        }
        
        .status-busy {
            color: #dc3545;
            font-weight: 500;
        }

        .user-stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .user-stats .stat-card {
            flex: 1;
            display: flex;
            align-items: center;
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .user-stats .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: rgba(12, 77, 162, 0.1);
            color: #0c4da2;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-right: 20px;
        }
        
        .user-stats .stat-info {
            flex: 1;
        }
        
        .user-stats .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #0c4da2;
        }
        
        .user-stats .stat-title {
            font-size: 16px;
            color: #666;
        }
        
        /* 紧凑型统计卡片样式 */
        .user-stats-compact {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card-small {
            flex: 1;
            display: flex;
            align-items: center;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 12px 15px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
            max-width: 220px;
        }
        
        /* 可点击卡片样式 */
        .stat-card-small.clickable {
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .stat-card-small.clickable:hover {
            background-color: #e9f0fd;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }
        
        .stat-icon-small {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(12, 77, 162, 0.1);
            color: #0c4da2;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            margin-right: 12px;
        }
        
        .stat-info-small {
            flex: 1;
        }
        
        .stat-value-small {
            font-size: 22px;
            font-weight: bold;
            color: #0c4da2;
            line-height: 1.2;
        }
        
        .stat-title-small {
            font-size: 12px;
            color: #666;
        }
        
        /* 用户表格样式 */
        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .table-header {
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #eee;
        }
        
        .table-header h3 {
            margin: 0;
            font-size: 18px;
            color: #0c4da2;
        }
        
        .table-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .search-box {
            position: relative;
        }
        
        .search-box input {
            padding: 8px 12px;
            padding-right: 30px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        
        .search-box i {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #777;
        }
        
        .table-container {
            overflow-x: auto;
            padding: 0 5px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th, .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .data-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .data-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .data-table .btn-view,
        .data-table .btn-edit,
        .data-table .btn-delete {
            padding: 5px 8px;
            margin-right: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .data-table .btn-view {
            background-color: #e9f0fd;
            color: #0c4da2;
        }
        
        .data-table .btn-edit {
            background-color: #e9f7ef;
            color: #28a745;
        }
        
        .data-table .btn-delete {
            background-color: #feebed;
            color: #dc3545;
        }
        
        .table-footer {
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #eee;
        }
        
        .pagination {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .page-btn {
            width: 30px;
            height: 30px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #fff;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .page-info {
            font-size: 14px;
            color: #666;
        }
        
        .page-size {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-size: 14px;
        }
        
        .page-size select {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .table-empty {
            text-align: center;
            color: #888;
            padding: 30px 0;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: #fff;
            border-radius: 8px;
            width: 80%;
            max-width: 900px;
            max-height: 80%;
            display: flex;
            flex-direction: column;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: #0c4da2;
        }

        .close-modal {
            font-size: 24px;
            font-weight: bold;
            color: #888;
            cursor: pointer;
        }

        .close-modal:hover {
            color: #333;
        }

        .modal-body {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
        }

        .btn-sort {
            background-color: #f0f8ff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 3px 8px;
            font-size: 12px;
            cursor: pointer;
            margin-left: 10px;
            display: inline-flex;
            align-items: center;
            color: #0c4da2;
        }
        .btn-sort i {
            margin-right: 5px;
        }
        .btn-sort:hover {
            background-color: #e6f2ff;
        }
        
        .date-filter {
            display: flex;
            align-items: center;
            margin-right: 15px;
        }
        
        .date-input {
            height: 32px;
            border: 1px solid #ddd;
            border-radius: 4px 0 0 4px;
            padding: 0 8px;
            font-size: 14px;
            width: 150px;
        }
        
        .filter-btn {
            height: 32px;
            width: 32px;
            border: 1px solid #ddd;
            border-left: none;
            background-color: #f8f9fa;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .filter-btn:last-child {
            border-radius: 0 4px 4px 0;
        }
        
        .filter-btn:hover {
            background-color: #e9ecef;
        }
        
        .date-filter-active .date-input,
        .date-filter-active .filter-btn {
            border-color: #0c4da2;
        }
        
        .date-filter-active #applyDateFilter {
            background-color: #0c4da2;
            color: white;
        }
        
        /* 用户注册日期高亮样式 */
        .flatpickr-day.has-registrations {
            background-color: #e8f5e9;
            border-color: #c8e6c9;
            position: relative;
        }
        
        .flatpickr-day.has-registrations:hover {
            background-color: #c8e6c9;
        }
        
        .registration-count {
            position: absolute;
            bottom: 2px;
            right: 4px;
            font-size: 9px;
            color: #2e7d32;
            font-weight: bold;
        }
        
        /* 订单日期高亮样式 */
        .flatpickr-day.has-orders {
            background-color: #e3f2fd;
            border-color: #bbdefb;
            position: relative;
        }
        
        .flatpickr-day.has-orders:hover {
            background-color: #bbdefb;
        }
        
        .order-count {
            position: absolute;
            bottom: 2px;
            right: 4px;
            font-size: 9px;
            color: #0d47a1;
            font-weight: bold;
        }
        
        /* 注册天数筛选样式 */
        .days-filter {
            display: flex;
            align-items: center;
            margin-right: 15px;
        }
        
        .days-input {
            height: 32px;
            border: 1px solid #ddd;
            border-radius: 4px 0 0 4px;
            padding: 0 8px;
            font-size: 14px;
            width: 100px;
        }
        
        .days-filter-active .days-input,
        .days-filter-active .filter-btn {
            border-color: #0c4da2;
        }
        
        .days-filter-active #applyDaysFilter {
            background-color: #0c4da2;
            color: white;
        }

        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
        }
        
        .chart-options {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        
        .chart-range-btn {
            padding: 8px 15px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .chart-range-btn:hover {
            background-color: #e9f0fd;
        }
        
        .chart-range-btn.active {
            background-color: #0c4da2;
            color: white;
            border-color: #0c4da2;
        }
        
        /* 图表控制按钮样式 */
        .chart-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            flex-direction: column;
            gap: 5px;
            z-index: 10;
        }
        
        .chart-control-btn {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 0.9);
            border: 1px solid #ddd;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #0c4da2;
            transition: all 0.2s;
        }
        
        .chart-control-btn:hover {
            background-color: #e9f0fd;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        /* 图表滚动条样式 */
        .chart-scroll-container {
            width: 100%;
            height: 20px;
            margin-top: 10px;
            padding: 0 10px;
        }
        
        .chart-scroll-track {
            width: 100%;
            height: 8px;
            background-color: #f0f0f0;
            border-radius: 4px;
            position: relative;
        }
        
        .chart-scroll-thumb {
            position: absolute;
            height: 100%;
            background-color: #0c4da2;
            border-radius: 4px;
            cursor: pointer;
            min-width: 50px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏导航 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="img/logo.jpg" alt="金舟国际物流" class="logo-img">
                <h1 class="sidebar-title">金舟国际物流</h1>
                <span class="admin-badge">管理员控制台</span>
            </div>
            
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#" class="nav-link active" data-page="dashboard">
                        <span class="nav-icon"><i class="fas fa-tachometer-alt"></i></span>
                        <span class="nav-text">控制面板</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="orders">
                        <span class="nav-icon"><i class="fas fa-shopping-cart"></i></span>
                        <span class="nav-text">订单管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="users">
                        <span class="nav-icon"><i class="fas fa-users"></i></span>
                        <span class="nav-text">用户管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="merchants">
                        <span class="nav-icon"><i class="fas fa-store"></i></span>
                        <span class="nav-text">商品管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="customer-service">
                        <span class="nav-icon"><i class="fas fa-headset"></i></span>
                        <span class="nav-text">客服管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="products">
                        <span class="nav-icon"><i class="fas fa-warehouse"></i></span>
                        <span class="nav-text">仓库管理</span>
                    </a>
                </li>
            </ul>
        </aside>
        
        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="header">
                <h2 class="page-title" id="pageTitle">控制面板</h2>
                
                <div class="header-actions">
                    <div class="admin-info">
                        <div class="admin-avatar" id="adminInitial">A</div>
                        <span class="admin-name" id="adminName">管理员</span>
                    </div>
                    
                    <button class="logout-btn" id="logoutBtn">
                        <span class="logout-icon"><i class="fas fa-sign-out-alt"></i></span>
                        退出
                    </button>
                </div>
            </div>
            
            <!-- 控制面板内容 -->
            <div id="dashboardContent" class="page-content active">
                <!-- 控制面板内容在这里 -->
            </div>
            
            <!-- 订单管理内容 -->
            <div id="ordersContent" class="page-content">
                <div class="card">
                    <h3>订单管理</h3>
                    <p>在这里可以查看和管理所有订单。</p>
                    
                    <!-- 添加搜索框和日期筛选 -->
                    <div class="table-header" style="padding: 10px 0; display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <div>
                            <button id="toggleOrderSortBtn" class="btn-sort" title="切换排序">
                                <i class="fas fa-sort-amount-down"></i>
                                <span>最新优先</span>
                            </button>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <div class="date-filter">
                                <input type="text" id="orderDateFilter" class="date-input" title="按下单日期筛选" placeholder="选择日期" readonly>
                                <button class="filter-btn" id="applyOrderDateFilter" title="应用日期筛选"><i class="fas fa-filter"></i></button>
                                <button class="filter-btn" id="clearOrderDateFilter" title="清除日期筛选"><i class="fas fa-times"></i></button>
                            </div>
                            <div class="search-box">
                                <input type="text" placeholder="搜索订单..." id="orderSearchInput">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 订单列表将在此显示 -->
                    <table class="order-table">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>用户</th>
                                <th>收货国家</th>
                                <th>下单时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="ordersList">
                            <!-- 订单数据将通过JavaScript动态加载 -->
                            <tr>
                                <td colspan="6">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <!-- 添加分页控制 -->
                    <div class="table-footer" style="margin-top: 15px;">
                        <div class="pagination">
                            <button class="page-btn" id="prevOrderPage" disabled><i class="fas fa-angle-left"></i></button>
                            <span class="page-info">第 <span id="currentOrderPage">1</span> 页，共 <span id="totalOrderPages">1</span> 页</span>
                            <div class="page-jump">
                                <input type="number" id="jumpToOrderPage" min="1" placeholder="页码" title="输入页码后按回车跳转">
                                <button class="jump-btn" id="jumpOrderBtn" title="跳转到指定页码"><i class="fas fa-arrow-right"></i></button>
                            </div>
                            <button class="page-btn" id="nextOrderPage" disabled><i class="fas fa-angle-right"></i></button>
                        </div>
                        <div class="page-size">
                            每页显示:
                            <select id="orderPageSize">
                                <option value="10" selected>10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 用户管理内容 -->
            <div id="usersContent" class="page-content">
                <!-- 用户统计信息 -->
                <div class="user-stats-compact">
                    <div class="stat-card-small clickable" id="totalUsersCard">
                        <div class="stat-icon-small"><i class="fas fa-users"></i></div>
                        <div class="stat-info-small">
                            <div class="stat-value-small" id="totalUsers">0</div>
                            <div class="stat-title-small">总用户数</div>
                        </div>
                    </div>
                    <div class="stat-card-small clickable" id="newUsersCard">
                        <div class="stat-icon-small"><i class="fas fa-user-plus"></i></div>
                        <div class="stat-info-small">
                            <div class="stat-value-small" id="newUsers">0</div>
                            <div class="stat-title-small">今日新增用户</div>
                        </div>
                    </div>
                    <div class="stat-card-small" id="blacklistedUserCard" style="cursor: pointer;">
                        <div class="stat-icon-small"><i class="fas fa-user-lock"></i></div>
                        <div class="stat-info-small">
                            <div class="stat-value-small" id="blacklistedUsers">0</div>
                            <div class="stat-title-small">黑名单用户</div>
                        </div>
                    </div>
                </div>
                
                <!-- 添加黑名单用户弹窗 -->
                <div id="blacklistModal" class="modal" style="display: none;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>黑名单用户</h3>
                            <span class="close-modal">&times;</span>
                        </div>
                        <div class="modal-body">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>用户ID</th>
                                        <th>用户名</th>
                                        <th>邮箱/微信</th>
                                        <th>注册日期</th>
                                        <th>拉黑日期</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="blacklistedUserTableBody">
                                    <!-- 黑名单用户数据将在这里动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- 今日新增用户弹窗 -->
                <div id="newUsersModal" class="modal" style="display: none;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>今日新增用户</h3>
                            <span class="close-modal">&times;</span>
                        </div>
                        <div class="modal-body">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>用户ID</th>
                                        <th>用户名</th>
                                        <th>邮箱/微信</th>
                                        <th>注册时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="newUsersTableBody">
                                    <!-- 今日新增用户数据将在这里动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- 用户注册趋势图弹窗 -->
                <div id="registrationChartModal" class="modal" style="display: none;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>用户注册趋势分析</h3>
                            <button id="downloadChartData" class="btn-download" title="下载数据">
                                <i class="fas fa-download"></i> 下载表格
                            </button>
                            <span class="close-modal">&times;</span>
                        </div>
                        <style>
                            .btn-download {
                                background-color: #28a745;
                                color: white;
                                border: none;
                                border-radius: 4px;
                                padding: 6px 12px;
                                font-size: 14px;
                                cursor: pointer;
                                display: flex;
                                align-items: center;
                                gap: 5px;
                                margin-right: 15px;
                                transition: background-color 0.2s;
                            }
                            .btn-download:hover {
                                background-color: #218838;
                            }
                            .modal-header {
                                display: flex;
                                align-items: center;
                            }
                            .modal-header h3 {
                                flex: 1;
                            }
                        </style>
                        <div class="modal-body">
                            <div class="chart-container" style="position: relative; height:400px; width:100%">
                                <canvas id="registrationChart"></canvas>
                                <div class="chart-controls">
                                    <button id="zoomInBtn" class="chart-control-btn" title="放大">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button id="zoomOutBtn" class="chart-control-btn" title="缩小">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button id="resetZoomBtn" class="chart-control-btn" title="重置视图">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                </div>
                            </div>
                            <div id="chartScrollContainer" class="chart-scroll-container" style="display: none;">
                                <div class="chart-scroll-track">
                                    <div class="chart-scroll-thumb"></div>
                                </div>
                            </div>
                            <div class="chart-options" style="margin-top: 20px; display: flex; justify-content: center; gap: 20px;">
                                <button class="chart-range-btn" data-range="7">最近7天</button>
                                <button class="chart-range-btn" data-range="30">最近30天</button>
                                <button class="chart-range-btn" data-range="90">最近90天</button>
                                <button class="chart-range-btn" data-range="365">最近一年</button>
                            </div>
                        </div>
                    </div>
                </div>

                <style>
                    .modal {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background-color: rgba(0, 0, 0, 0.5);
                        z-index: 1000;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }

                    .modal-content {
                        background-color: #fff;
                        border-radius: 8px;
                        width: 80%;
                        max-width: 900px;
                        max-height: 80%;
                        display: flex;
                        flex-direction: column;
                        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
                    }

                    .modal-header {
                        padding: 15px 20px;
                        border-bottom: 1px solid #eee;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }

                    .modal-header h3 {
                        margin: 0;
                        color: #0c4da2;
                    }

                    .close-modal {
                        font-size: 24px;
                        font-weight: bold;
                        color: #888;
                        cursor: pointer;
                    }

                    .close-modal:hover {
                        color: #333;
                    }

                    .modal-body {
                        padding: 20px;
                        overflow-y: auto;
                        flex: 1;
                    }
                    
                    .chart-range-btn {
                        padding: 8px 15px;
                        background-color: #f8f9fa;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        cursor: pointer;
                        transition: all 0.2s;
                    }
                    
                    .chart-range-btn:hover {
                        background-color: #e9f0fd;
                    }
                    
                    .chart-range-btn.active {
                        background-color: #0c4da2;
                        color: white;
                        border-color: #0c4da2;
                    }
                </style>
                
                <!-- 用户信息表格 -->
                <div class="card">
                    <div class="table-header">
                        <h3>用户信息列表 
                            <button id="toggleSortBtn" class="btn-sort" title="切换排序">
                                <i class="fas fa-sort-amount-down"></i>
                                <span>最新优先</span>
                            </button>
                        </h3>
                        <div class="table-actions">
                            <div class="date-filter">
                                <input type="text" id="dateFilter" class="date-input" title="按注册日期筛选" placeholder="选择日期" readonly>
                                <button class="filter-btn" id="applyDateFilter" title="应用日期筛选"><i class="fas fa-filter"></i></button>
                                <button class="filter-btn" id="clearDateFilter" title="清除日期筛选"><i class="fas fa-times"></i></button>
                            </div>
                            <div class="days-filter">
                                <input type="number" id="daysFilter" class="days-input" title="按注册天数筛选" placeholder="注册天数" min="0">
                                <button class="filter-btn" id="applyDaysFilter" title="应用天数筛选"><i class="fas fa-filter"></i></button>
                                <button class="filter-btn" id="clearDaysFilter" title="清除天数筛选"><i class="fas fa-times"></i></button>
                            </div>
                            <div class="search-box">
                                <input type="text" placeholder="搜索用户..." id="userSearchInput">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                    </div>

                    <style>
                        .btn-sort {
                            background-color: #f0f8ff;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            padding: 3px 8px;
                            font-size: 12px;
                            cursor: pointer;
                            margin-left: 10px;
                            display: inline-flex;
                            align-items: center;
                            color: #0c4da2;
                        }
                        .btn-sort i {
                            margin-right: 5px;
                        }
                        .btn-sort:hover {
                            background-color: #e6f2ff;
                        }
                        
                        .date-filter {
                            display: flex;
                            align-items: center;
                            margin-right: 15px;
                        }
                        
                        .date-input {
                            height: 32px;
                            border: 1px solid #ddd;
                            border-radius: 4px 0 0 4px;
                            padding: 0 8px;
                            font-size: 14px;
                            width: 150px;
                        }
                        
                        .filter-btn {
                            height: 32px;
                            width: 32px;
                            border: 1px solid #ddd;
                            border-left: none;
                            background-color: #f8f9fa;
                            cursor: pointer;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                        
                        .filter-btn:last-child {
                            border-radius: 0 4px 4px 0;
                        }
                        
                        .filter-btn:hover {
                            background-color: #e9ecef;
                        }
                        
                        .date-filter-active .date-input,
                        .date-filter-active .filter-btn {
                            border-color: #0c4da2;
                        }
                        
                        .date-filter-active #applyDateFilter {
                            background-color: #0c4da2;
                            color: white;
                        }
                    </style>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>用户ID</th>
                                    <th>用户名</th>
                                    <th>邮箱/微信</th>
                                    <th>注册日期</th>
                                    <th>注册天数</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="userTableBody">
                                <!-- 用户数据将在这里动态加载 -->
                                <tr>
                                    <td colspan="6" class="table-empty">加载用户数据...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="table-footer">
                        <div class="pagination">
                            <button class="page-btn" id="prevPage" disabled><i class="fas fa-angle-left"></i></button>
                            <span class="page-info">第 <span id="currentPage">1</span> 页，共 <span id="totalPages">1</span> 页</span>
                            <div class="page-jump">
                                <input type="number" id="jumpToPage" min="1" placeholder="页码" title="输入页码后按回车跳转">
                                <button class="jump-btn" id="jumpBtn" title="跳转到指定页码"><i class="fas fa-arrow-right"></i></button>
                            </div>
                            <button class="page-btn" id="nextPage" disabled><i class="fas fa-angle-right"></i></button>
                        </div>
                        <div class="page-size">
                            每页显示:
                            <select id="pageSize">
                                <option value="10">10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                            </select>
                        </div>
                    </div>
                    
                    <style>
                        .page-jump {
                            display: flex;
                            align-items: center;
                            margin: 0 10px;
                        }
                        
                        .page-jump input {
                            width: 80px;
                            height: 28px;
                            border: 1px solid #ddd;
                            border-radius: 4px 0 0 4px;
                            padding: 0 8px;
                            text-align: center;
                            font-size: 14px;
                        }
                        
                        .jump-btn {
                            height: 28px;
                            width: 28px;
                            border: 1px solid #ddd;
                            border-left: none;
                            border-radius: 0 4px 4px 0;
                            background-color: #f8f9fa;
                            cursor: pointer;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                        
                        .jump-btn:hover {
                            background-color: #e9ecef;
                        }
                    </style>
                </div>
            </div>
            
            <!-- 商品管理内容 -->
            <div id="merchantsContent" class="page-content">
                <div class="card">
                    <!-- 商品管理工具栏 -->
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <div>
                            <button class="btn-add" id="publishProduct">
                                <i class="fas fa-plus-circle"></i> 发布商品
                            </button>
                            <button class="btn-add" id="addKeywords" style="margin-left: 10px; background-color: #28a745;">
                                <i class="fas fa-tag"></i> 添加关键词
                            </button>
                            <button class="btn-add" id="manageCategories" style="margin-left: 10px; background-color: #17a2b8;">
                                <i class="fas fa-list"></i> 商品分类
                            </button>
                            <button class="btn-add" id="manageCarousels" style="margin-left: 10px; background-color: #ff6b6b;">
                                <i class="fas fa-images"></i> 轮播图管理
                            </button>
                        </div>
                    </div>
                    
                    <!-- 商品列表区域 -->
                    <div id="productManagementArea">
                        <!-- 已发布商品列表 -->
                        <div class="published-products-section">
                            <!-- 标题和搜索区域 -->
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h4 style="margin: 0; color: #333;">已发布商品</h4>
                                <div class="search-box" style="position: relative;">
                                    <input type="text" id="publishedProductsSearchInput" placeholder="搜索商品..."
                                           style="width: 200px; padding: 8px 35px 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                                    <i class="fas fa-search" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #999; pointer-events: none;"></i>
                                </div>
                            </div>

                            <!-- 商品表格 -->
                            <div class="products-table-container">
                                <table class="products-table" id="productsTable" style="display: none;">
                                    <thead>
                                        <tr>
                                            <th style="width: 80px;">商品图片</th>
                                            <th>商品名称</th>
                                            <th style="width: 100px;">价格 ¥</th>
                                            <th style="width: 80px;">库存 ↑</th>
                                            <th style="width: 80px;">累计销量 ⓘ</th>
                                            <th style="width: 100px;">30日销量 ⓘ</th>
                                            <th style="width: 120px;">创建时间 ↑</th>
                                            <th style="width: 100px;">操作 ⓘ</th>
                                        </tr>
                                    </thead>
                                    <tbody id="productsTableBody">
                                        <!-- 商品行将通过JavaScript动态添加 -->
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页控制 -->
                            <div class="table-footer" id="publishedProductsPagination" style="margin-top: 15px; display: none;">
                                <div class="pagination">
                                    <button class="page-btn" id="prevPublishedPage" disabled><i class="fas fa-angle-left"></i></button>
                                    <span class="page-info">第 <span id="currentPublishedPage">1</span> 页，共 <span id="totalPublishedPages">1</span> 页</span>
                                    <div class="page-jump">
                                        <input type="number" id="jumpToPublishedPage" min="1" placeholder="页码" title="输入页码后按回车跳转">
                                        <button class="jump-btn" id="jumpPublishedBtn" title="跳转到指定页码"><i class="fas fa-arrow-right"></i></button>
                                    </div>
                                    <button class="page-btn" id="nextPublishedPage" disabled><i class="fas fa-angle-right"></i></button>
                                </div>
                                <div class="page-size">
                                    每页显示:
                                    <select id="publishedPageSize">
                                        <option value="10" selected>10</option>
                                        <option value="20">20</option>
                                        <option value="50">50</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 无商品提示 -->
                            <div id="noPublishedProducts" style="display: none; text-align: center; padding: 40px; color: #666;">
                                <i class="fas fa-box-open" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                                <p>暂无已发布的商品</p>
                                <button onclick="window.location.href='publish-product.html'" style="margin-top: 15px; background: #52c41a; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                                    <i class="fas fa-plus"></i> 发布第一个商品
                                </button>
                            </div>

                            <!-- 加载状态 -->
                            <div id="productsLoading" style="text-align: center; padding: 40px; color: #666;">
                                <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 10px;"></i>
                                <p>正在加载商品...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 客服管理内容 -->
            <div id="productsContent" class="page-content">
                <div class="card">
                    <h3>仓库管理</h3>
                    <p>在这里可以查看和管理所有商品库存。</p>
                    
                    <!-- 商品列表 -->
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <div>
                            <button class="btn-add" id="addProduct">添加商品</button>
                        </div>
                        <div class="search-box">
                            <input type="text" placeholder="搜索商品..." id="productSearchInput">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                    
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>商品ID</th>
                                <th>商品名称</th>
                                <th>价格</th>
                                <th>库存</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="productsList">
                            <tr>
                                <td colspan="6" class="table-empty">暂无商品数据</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <!-- 分页控制 -->
                    <div class="table-footer" style="margin-top: 15px;">
                        <div class="pagination">
                            <button class="page-btn" id="prevProductPage" disabled><i class="fas fa-angle-left"></i></button>
                            <span class="page-info">第 <span id="currentProductPage">1</span> 页，共 <span id="totalProductPages">1</span> 页</span>
                            <div class="page-jump">
                                <input type="number" id="jumpToProductPage" min="1" placeholder="页码" title="输入页码后按回车跳转">
                                <button class="jump-btn" id="jumpProductBtn" title="跳转到指定页码"><i class="fas fa-arrow-right"></i></button>
                            </div>
                            <button class="page-btn" id="nextProductPage" disabled><i class="fas fa-angle-right"></i></button>
                        </div>
                        <div class="page-size">
                            每页显示:
                            <select id="productPageSize">
                                <option value="10" selected>10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <div id="customer-serviceContent" class="page-content">
                <div class="card">
                    <h3>客服管理</h3>
                    <p>在这里可以管理客服人员和工单。</p>
                    
                    <!-- 客服人员列表 -->
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <div>
                            <h4 class="section-title" style="margin: 0;">客服人员</h4>
                            <button class="btn-add" id="addCustomerService">添加客服</button>
                        </div>
                        <div class="search-box">
                            <input type="text" placeholder="搜索客服..." id="csSearchInput">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                    
                    <table class="cs-table">
                        <thead>
                            <tr>
                                <th>客服ID</th>
                                <th>姓名</th>
                                <th>电话</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="csStaffList">
                            <!-- 客服人员数据将通过JavaScript动态加载 -->
                            <tr>
                                <td colspan="6">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <!-- 分页控制 -->
                    <div class="table-footer" style="margin-top: 15px;">
                        <div class="pagination">
                            <button class="page-btn" id="prevCSPage" disabled><i class="fas fa-angle-left"></i></button>
                            <span class="page-info">第 <span id="currentCSPage">1</span> 页，共 <span id="totalCSPages">1</span> 页</span>
                            <div class="page-jump">
                                <input type="number" id="jumpToCSPage" min="1" placeholder="页码" title="输入页码后按回车跳转">
                                <button class="jump-btn" id="jumpCSBtn" title="跳转到指定页码"><i class="fas fa-arrow-right"></i></button>
                            </div>
                            <button class="page-btn" id="nextCSPage" disabled><i class="fas fa-angle-right"></i></button>
                        </div>
                        <div class="page-size">
                            每页显示:
                            <select id="csPageSize">
                                <option value="10" selected>10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                            </select>
                        </div>
                    </div>
                

                            </div>
                        </div>
            
        </main>
                            </div>
    
    <!-- 优雅提示的容器 -->
    <div class="toast-container" id="toast-container"></div>
    
    <!-- 添加商品模态框 -->
    <div id="addProductModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 500px; width: 90%;">
            <div class="modal-header">
                <h3>添加商品</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addProductForm">
                    <div style="margin-bottom: 15px;">
                        <label for="productName" style="display: block; margin-bottom: 5px; font-weight: bold;">商品名称</label>
                        <input type="text" id="productName" name="productName" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label for="productPrice" style="display: block; margin-bottom: 5px; font-weight: bold;">价格</label>
                        <input type="text" id="productPrice" name="productPrice" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label for="productStock" style="display: block; margin-bottom: 5px; font-weight: bold;">库存</label>
                        <input type="number" id="productStock" name="productStock" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label for="productStatus" style="display: block; margin-bottom: 5px; font-weight: bold;">状态</label>
                        <select id="productStatus" name="productStatus" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                            <option value="上架中">上架中</option>
                            <option value="缺货">缺货</option>
                            <option value="下架">下架</option>
                        </select>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label for="productDesc" style="display: block; margin-bottom: 5px; font-weight: bold;">商品描述</label>
                        <textarea id="productDesc" name="productDesc" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; height: 100px;"></textarea>
                    </div>
                    
                    <div style="text-align: right; margin-top: 20px;">
                        <button type="button" class="btn-cancel" style="background-color: #f5f5f5; color: #333; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-right: 10px;">取消</button>
                        <button type="submit" class="btn-submit" style="background-color: #28a745; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">提交</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 添加客服模态框 -->
    <div id="addCustomerServiceModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 500px; width: 90%;">
            <div class="modal-header">
                <h3>添加客服</h3>
                <span class="close-modal">&times;</span>
                        </div>
            <div class="modal-body">
                <form id="addCustomerServiceForm">
                    <div style="margin-bottom: 15px;">
                        <label for="csAccount" style="display: block; margin-bottom: 5px; font-weight: bold;">客服账号</label>
                        <input type="text" id="csAccount" name="csAccount" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                            </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label for="csPassword" style="display: block; margin-bottom: 5px; font-weight: bold;">密码</label>
                        <input type="password" id="csPassword" name="csPassword" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                        </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label for="csName" style="display: block; margin-bottom: 5px; font-weight: bold;">客服名称</label>
                        <input type="text" id="csName" name="csName" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label for="csEmail" style="display: block; margin-bottom: 5px; font-weight: bold;">邮箱</label>
                        <input type="email" id="csEmail" name="csEmail" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label for="csPhone" style="display: block; margin-bottom: 5px; font-weight: bold;">电话</label>
                        <input type="tel" id="csPhone" name="csPhone" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
            </div>
            
                    <div style="margin-bottom: 15px;">
                        <label for="csWechat" style="display: block; margin-bottom: 5px; font-weight: bold;">微信</label>
                        <input type="text" id="csWechat" name="csWechat" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
    </div>
    

                    
                    <div style="text-align: right; margin-top: 20px;">
                        <button type="button" class="btn-cancel" style="background-color: #f5f5f5; color: #333; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-right: 10px;">取消</button>
                        <button type="submit" class="btn-submit" style="background-color: #28a745; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">提交</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 引入Flatpickr日期选择器JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/zh.js"></script>
    
    <!-- 引入Chart.js图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    
    <!-- 全局图片查看器函数 -->
    <script>
                    // 图片查看器
            function openImageViewer(imgSrc, title) {
                // 创建图片查看器模态框
                const viewer = document.createElement('div');
                viewer.className = 'modal';
                viewer.style.display = 'flex';
                viewer.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';
                viewer.style.zIndex = '2000';
                
                viewer.innerHTML = `
                    <div style="position:relative; max-width:90%; max-height:90%; margin:auto; text-align:center;">
                        <div style="position:absolute; top:-40px; left:0; right:0; display:flex; justify-content:space-between; color:white;">
                            <h3 style="margin:0;">${title || '图片查看'}</h3>
                            <div>
                                <span style="color:white; font-size:14px; margin-right:15px;">
                                    <i class="fas fa-mouse"></i> 使用鼠标滚轮缩放
                                </span>
                                <button class="close-viewer" style="background:none; border:none; color:white; font-size:24px; cursor:pointer;" title="关闭">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <img src="${imgSrc}" style="max-width:100%; max-height:80vh; object-fit:contain;" id="viewer-image">
                    </div>
                `;
                
                document.body.appendChild(viewer);
                
                // 图片加载完成后进行初始化
                const img = viewer.querySelector('#viewer-image');
                img.onload = function() {
                    // 图片已加载，可以进行后续操作
                    console.log('图片已加载，尺寸:', img.naturalWidth, 'x', img.naturalHeight);
                };
                
                // 添加缩放功能
                let scale = 1;
                
                // 添加鼠标滚轮缩放功能
                viewer.addEventListener('wheel', function(e) {
                    // 阻止默认行为（页面滚动）
                    e.preventDefault();
                    
                    // 根据滚轮方向放大或缩小
                    if (e.deltaY < 0) {
                        // 向上滚动，放大
                        scale += 0.1;
                    } else {
                        // 向下滚动，缩小
                        if (scale > 0.2) {
                            scale -= 0.1;
                        }
                    }
                    
                    // 应用缩放
                    img.style.transform = `scale(${scale})`;
                });
                
                // 点击空白区域关闭查看器
                viewer.addEventListener('click', function(e) {
                    if (e.target === viewer) {
                        document.body.removeChild(viewer);
                    }
                });
                
                // 关闭按钮事件
                viewer.querySelector('.close-viewer').addEventListener('click', function() {
                    document.body.removeChild(viewer);
                });
                
                // 添加键盘快捷键
                const handleKeyDown = function(e) {
                    if (e.key === 'Escape') {
                        document.body.removeChild(viewer);
                        document.removeEventListener('keydown', handleKeyDown);
                    } else if (e.key === '+' || e.key === '=') {
                        scale += 0.1;
                        img.style.transform = `scale(${scale})`;
                    } else if (e.key === '-' && scale > 0.2) {
                        scale -= 0.1;
                        img.style.transform = `scale(${scale})`;
                    }
                };
                
                document.addEventListener('keydown', handleKeyDown);
            }
    </script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查管理员登录状态
            const checkAdminAuth = () => {
                const adminData = JSON.parse(sessionStorage.getItem('loggedInAdmin') || '{}');
                
                if (!adminData.isAdmin && !adminData.isCustomerService) {
                    // 未登录或既不是管理员也不是客服，重定向到登录页
                    window.location.href = 'admin-login.html';
                    return null;
                }
                
                return adminData;
            };
            
            // 初始化页面
            const initPage = () => {
                const adminData = checkAdminAuth();
                if (!adminData) return;
                
                // 设置管理员信息
                const adminInitial = document.getElementById('adminInitial');
                const adminName = document.getElementById('adminName');
                
                if (adminData.username) {
                    // 根据是否是客服账号显示不同的名称
                    adminName.textContent = adminData.isCustomerService ? (adminData.name || adminData.username) : adminData.username;
                    adminInitial.textContent = adminData.isCustomerService ? 
                        (adminData.name ? adminData.name.charAt(0).toUpperCase() : adminData.username.charAt(0).toUpperCase()) : 
                        adminData.username.charAt(0).toUpperCase();
                }
                
                // 如果是客服账号，限制访问某些功能
                let defaultPage = 'dashboard';
                if (adminData.isCustomerService) {
                    // 只显示客服相关的功能，隐藏其他管理功能
                    const navItems = document.querySelectorAll('.nav-item');
                    navItems.forEach(item => {
                        const link = item.querySelector('.nav-link');
                        const targetPage = link.getAttribute('data-page');
                        
                        // 只保留仪表盘和客服管理页面
                        if (targetPage !== 'dashboard' && targetPage !== 'customer-service') {
                            item.style.display = 'none';
                        }
                    });
                    
                    // 默认显示客服管理页面
                    defaultPage = 'customer-service';
                }
                
                // 检查是否需要显示欢迎信息（只在首次登录时显示）
                if (sessionStorage.getItem('welcomeShown') !== 'true') {
                    const displayName = adminData.isCustomerService ? (adminData.name || adminData.username) : adminData.username;
                    showToast(`欢迎回来，${displayName}！`, 'success', '登录成功');
                    // 设置标记，表示已经显示过欢迎信息
                    sessionStorage.setItem('welcomeShown', 'true');
                }
                
                // 从 localStorage 读取上次访问的页面，如果是客服则强制使用客服管理页面
                const lastVisitedPage = adminData.isCustomerService ? defaultPage : (localStorage.getItem('lastVisitedPage') || defaultPage);
                
                // 设置菜单切换
                setupMenuNavigation(lastVisitedPage);
                
                // 初始化当前页面
                activatePage(lastVisitedPage);
                
                // 设置用户分页事件处理程序
                setupPaginationHandlers();
                
                // 设置订单分页事件处理程序
                setupOrderPagination();
                
                // 设置商品管理分页控制
                setupMerchantPagination();
                
                // 设置客服管理分页控制
                setupCSPagination();
                
                // 设置商品管理分页控制
                setupProductPagination();

                // 设置已发布商品分页控制
                setupPublishedProductsPagination();

                // 初始化日期选择器
                initDatepicker();
                
                // 初始化订单日期选择器
                initOrderDatepicker();
            };
            
            // 激活指定的页面
            const activatePage = (pageName) => {
                // 更新导航菜单高亮
                document.querySelectorAll('.nav-link').forEach(link => {
                    if (link.getAttribute('data-page') === pageName) {
                        link.classList.add('active');
                    } else {
                        link.classList.remove('active');
                    }
                });
                
                // 隐藏所有内容
                document.querySelectorAll('.page-content').forEach(content => {
                    content.classList.remove('active');
                });
                
                // 显示目标内容
                document.getElementById(`${pageName}Content`).classList.add('active');
                
                // 更新页面标题
                const pageTitle = document.getElementById('pageTitle');
                if (pageName === 'dashboard') {
                    pageTitle.textContent = '控制面板';
                } else if (pageName === 'orders') {
                    pageTitle.textContent = '订单管理';
                    // 加载订单数据
                    loadOrdersData();
                } else if (pageName === 'users') {
                    pageTitle.textContent = '用户管理';
                    // 加载用户数据
                    loadUsersData();
                } else if (pageName === 'merchants') {
                    pageTitle.textContent = '商品管理';
                    // 加载商品管理数据
                    loadMerchantsData();
                } else if (pageName === 'customer-service') {
                    pageTitle.textContent = '客服管理';
                    // 加载客服管理数据
                    loadCustomerServiceData();
                } else if (pageName === 'products') {
                    pageTitle.textContent = '仓库管理';
                    // 加载商品数据
                    loadProductsData();
                }
                
                // 保存当前页面到 localStorage
                localStorage.setItem('lastVisitedPage', pageName);
            };
            
                            // 设置菜单导航
            const setupMenuNavigation = (activePage) => {
                const navLinks = document.querySelectorAll('.nav-link');
                
                navLinks.forEach(link => {
                    // 设置初始状态
                    if (link.getAttribute('data-page') === activePage) {
                        link.classList.add('active');
                    } else {
                        link.classList.remove('active');
                    }
                    
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        
                        // 获取目标页面
                        const targetPage = this.getAttribute('data-page');
                        
                        // 清除所有toast通知
                        const toastContainer = document.getElementById('toast-container');
                        if (toastContainer) {
                            toastContainer.innerHTML = '';
                        }
                        
                        // 激活目标页面
                        activatePage(targetPage);
                    });
                });
            };
            
            // 初始化Flatpickr日期选择器
            const initDatepicker = async () => {
                // 获取所有用户的注册日期统计
                const registrationStats = await fetchRegistrationStats();
                
                // 初始化日期选择器
                const dateFilterInput = document.getElementById('dateFilter');
                if (!dateFilterInput) return;
                
                // 从localStorage获取上次使用的日期
                const savedDateFilter = localStorage.getItem('userDateFilter');
                
                // 创建一个格式化日期的辅助函数，确保使用本地时区
                const formatDateToLocalYMD = (date) => {
                    const d = new Date(date);
                    const year = d.getFullYear();
                    const month = String(d.getMonth() + 1).padStart(2, '0');
                    const day = String(d.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                };
                
                // 初始化Flatpickr
                const fp = flatpickr(dateFilterInput, {
                    locale: "zh",
                    dateFormat: "Y-m-d",
                    allowInput: false,
                    disableMobile: true,
                    defaultDate: savedDateFilter || null,
                    onReady: function() {
                        // 如果有保存的日期，添加高亮样式
                        if (savedDateFilter) {
                            const dateFilterContainer = document.querySelector('.date-filter');
                            if (dateFilterContainer) {
                                dateFilterContainer.classList.add('date-filter-active');
                            }
                        }
                    },
                    onDayCreate: function(dObj, dStr, fp, dayElem) {
                        // 为了确保一致性，使用与筛选相同的格式化方法
                        const currentDate = dayElem.dateObj;
                        const year = currentDate.getFullYear();
                        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
                        const day = String(currentDate.getDate()).padStart(2, '0');
                        const dateStr = `${year}-${month}-${day}`;
                        
                        // 检查当前日期是否有用户注册
                        const count = registrationStats[dateStr] || 0;
                        
                        // 如果有注册，添加高亮样式和注册人数
                        if (count > 0) {
                            dayElem.classList.add('has-registrations');
                            
                            // 创建显示注册数量的元素
                            const countElem = document.createElement('span');
                            countElem.className = 'registration-count';
                            countElem.textContent = count;
                            dayElem.appendChild(countElem);
                        }
                    },
                    onChange: function(selectedDates, dateStr) {
                        if (dateStr) {
                            // 重置到第一页
                            document.getElementById('currentPage').textContent = '1';
                            
                            // 添加激活状态样式
                            const dateFilterContainer = document.querySelector('.date-filter');
                            if (dateFilterContainer) {
                                dateFilterContainer.classList.add('date-filter-active');
                            }
                            
                            // 保存到localStorage
                            localStorage.setItem('userDateFilter', dateStr);
                            
                            // 加载筛选后的数据
                            loadUsersData();
                        }
                    }
                });
                
                // 清除按钮事件
                const clearDateFilterBtn = document.getElementById('clearDateFilter');
                if (clearDateFilterBtn) {
                    clearDateFilterBtn.addEventListener('click', function() {
                        // 清空日期选择器
                        fp.clear();
                        
                        // 移除激活状态样式
                        const dateFilterContainer = document.querySelector('.date-filter');
                        if (dateFilterContainer) {
                            dateFilterContainer.classList.remove('date-filter-active');
                        }
                        
                        // 从localStorage中移除
                        localStorage.removeItem('userDateFilter');
                        
                        // 重置到第一页并重新加载数据
                        document.getElementById('currentPage').textContent = '1';
                        loadUsersData();
                    });
                }
                
                // 应用按钮事件
                const applyDateFilterBtn = document.getElementById('applyDateFilter');
                if (applyDateFilterBtn) {
                    applyDateFilterBtn.addEventListener('click', function() {
                        if (dateFilterInput.value) {
                            // 重置到第一页
                            document.getElementById('currentPage').textContent = '1';
                            // 重新加载数据
                            loadUsersData();
                        } else {
                            showToast('请选择日期', 'warning');
                        }
                    });
                }
            };
            
            // 获取用户注册日期统计数据
            const fetchRegistrationStats = async () => {
                try {
                    // 实际应用中应从服务器获取这些数据
                    // 这里先使用模拟数据
                    const response = await fetch('/api/users');
                    const data = await response.json();
                    
                    if (!data.success || !data.users) {
                        return {};
                    }
                    
                    // 统计每天的注册人数
                    const stats = {};
                    data.users.forEach(user => {
                        if (user.registrationTime) {
                            // 使用一致的本地时区日期格式
                            const date = new Date(user.registrationTime);
                            const year = date.getFullYear();
                            const month = String(date.getMonth() + 1).padStart(2, '0');
                            const day = String(date.getDate()).padStart(2, '0');
                            const dateStr = `${year}-${month}-${day}`;
                            
                            if (!stats[dateStr]) {
                                stats[dateStr] = 0;
                            }
                            
                            stats[dateStr]++;
                        }
                    });
                    
                    console.log('注册统计数据:', stats); // 调试用，查看实际统计结果
                    return stats;
                } catch (error) {
                    console.error('获取用户注册统计数据失败:', error);
                    return {};
                }
            };
            
            // 获取订单日期统计数据
            const fetchOrderStats = async () => {
                try {
                    // 从服务器获取订单数据
                    const response = await fetch('/api/get-orders');
                    const result = await response.json();
                    
                    if (!result.success || !result.data) {
                        return {};
                    }
                    
                    const orders = result.data;
                    
                    // 统计每天的订单数量
                    const stats = {};
                    orders.forEach(order => {
                        if (order.date) {
                            // 使用一致的本地时区日期格式
                            const orderDate = new Date(order.date);
                            const year = orderDate.getFullYear();
                            const month = String(orderDate.getMonth() + 1).padStart(2, '0');
                            const day = String(orderDate.getDate()).padStart(2, '0');
                            const dateStr = `${year}-${month}-${day}`;
                            
                            if (!stats[dateStr]) {
                                stats[dateStr] = 0;
                            }
                            
                            stats[dateStr]++;
                        }
                    });
                    
                    console.log('订单统计数据:', stats); // 调试用，查看实际统计结果
                    return stats;
                } catch (error) {
                    console.error('获取订单统计数据失败:', error);
                    return {};
                }
            };
            
            // 设置分页事件处理程序
            const setupPaginationHandlers = () => {
                // 处理页面切换
                const prevPageBtn = document.getElementById('prevPage');
                const nextPageBtn = document.getElementById('nextPage');
                
                if (prevPageBtn) {
                    prevPageBtn.addEventListener('click', function() {
                        if (!this.disabled) {
                            const currentPage = parseInt(document.getElementById('currentPage').textContent);
                            if (currentPage > 1) {
                                document.getElementById('currentPage').textContent = currentPage - 1;
                                loadUsersData();
                            }
                        }
                    });
                }
                
                if (nextPageBtn) {
                    nextPageBtn.addEventListener('click', function() {
                        if (!this.disabled) {
                            const currentPage = parseInt(document.getElementById('currentPage').textContent);
                            const totalPages = parseInt(document.getElementById('totalPages').textContent);
                            if (currentPage < totalPages) {
                                document.getElementById('currentPage').textContent = currentPage + 1;
                                loadUsersData();
                            }
                        }
                    });
                }
                
                // 处理每页显示数量变化
                const pageSizeSelect = document.getElementById('pageSize');
                if (pageSizeSelect) {
                    pageSizeSelect.addEventListener('change', function() {
                        document.getElementById('currentPage').textContent = '1'; // 切换到第一页
                        loadUsersData();
                    });
                }
                
                // 处理排序切换按钮
                const toggleSortBtn = document.getElementById('toggleSortBtn');
                if (toggleSortBtn) {
                    toggleSortBtn.addEventListener('click', function() {
                        // 获取当前排序方式并切换
                        const currentSortNewestFirst = localStorage.getItem('userSortNewestFirst') !== 'false';
                        const newSortNewestFirst = !currentSortNewestFirst;
                        
                        // 保存新的排序方式
                        localStorage.setItem('userSortNewestFirst', newSortNewestFirst.toString());
                        
                        // 重新加载数据
                        loadUsersData();
                    });
                }
                
                // 处理页面跳转
                const jumpToPageInput = document.getElementById('jumpToPage');
                const jumpBtn = document.getElementById('jumpBtn');
                
                // 处理跳转按钮点击
                if (jumpBtn) {
                    jumpBtn.addEventListener('click', function() {
                        handleJumpToPage();
                    });
                }
                
                // 处理输入框回车键
                if (jumpToPageInput) {
                    jumpToPageInput.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            handleJumpToPage();
                        }
                    });
                }
                
                // 页面跳转处理函数
                const handleJumpToPage = () => {
                    const jumpToPage = parseInt(jumpToPageInput.value);
                    const totalPages = parseInt(document.getElementById('totalPages').textContent);
                    
                    if (jumpToPage && jumpToPage > 0 && jumpToPage <= totalPages) {
                        document.getElementById('currentPage').textContent = jumpToPage;
                        loadUsersData();
                        // 清空输入框
                        jumpToPageInput.value = '';
                    } else {
                        // 输入无效
                        showToast('请输入有效的页码', 'warning');
                        jumpToPageInput.focus();
                    }
                };
                
                // 处理用户搜索
                const userSearchInput = document.getElementById('userSearchInput');
                if (userSearchInput) {
                    // 使用防抖函数优化搜索性能
                    let searchTimeout;
                    userSearchInput.addEventListener('input', function() {
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(() => {
                            // 重置到第一页
                            document.getElementById('currentPage').textContent = '1';
                            // 重新加载带有搜索条件的数据
                            loadUsersData();
                        }, 300); // 300毫秒防抖
                    });
                }
                
                // 处理注册天数筛选
                const daysFilterInput = document.getElementById('daysFilter');
                const applyDaysFilterBtn = document.getElementById('applyDaysFilter');
                const clearDaysFilterBtn = document.getElementById('clearDaysFilter');
                const daysFilterContainer = document.querySelector('.days-filter');
                
                // 应用注册天数筛选
                if (applyDaysFilterBtn) {
                    applyDaysFilterBtn.addEventListener('click', function() {
                        const days = daysFilterInput.value.trim();
                        if (days !== '') {
                            // 保存当前筛选天数到本地存储
                            localStorage.setItem('userDaysFilter', days);
                            
                            // 添加激活状态样式
                            daysFilterContainer.classList.add('days-filter-active');
                            
                            // 重置到第一页
                            document.getElementById('currentPage').textContent = '1';
                            
                            // 清除日期筛选，因为不能同时使用两种筛选
                            const dateFilterInput = document.getElementById('dateFilter');
                            const dateFilterContainer = document.querySelector('.date-filter');
                            if (dateFilterInput && dateFilterContainer) {
                                dateFilterInput.value = '';
                                dateFilterContainer.classList.remove('date-filter-active');
                                localStorage.removeItem('userDateFilter');
                            }
                            
                            // 重新加载数据
                            loadUsersData();
                        } else {
                            showToast('请输入注册天数', 'warning');
                        }
                    });
                }
                
                // 清除注册天数筛选
                if (clearDaysFilterBtn) {
                    clearDaysFilterBtn.addEventListener('click', function() {
                        if (daysFilterInput.value) {
                            // 清空输入
                            daysFilterInput.value = '';
                            
                            // 移除激活状态样式
                            daysFilterContainer.classList.remove('days-filter-active');
                            
                            // 从localStorage中移除
                            localStorage.removeItem('userDaysFilter');
                            
                            // 重置到第一页并重新加载数据
                            document.getElementById('currentPage').textContent = '1';
                            loadUsersData();
                        }
                    });
                }
                
                // 从localStorage恢复上次使用的天数筛选
                const savedDaysFilter = localStorage.getItem('userDaysFilter');
                if (savedDaysFilter && daysFilterInput) {
                    daysFilterInput.value = savedDaysFilter;
                    if (savedDaysFilter && daysFilterContainer) {
                        daysFilterContainer.classList.add('days-filter-active');
                    }
                }
            };
            
            // 原有的setupMenuNavigation函数已被替换为上方的新版本
            
            // 加载订单数据
            const loadOrdersData = () => {
                // 获取订单列表元素
                const ordersList = document.getElementById('ordersList');
                
                if (!ordersList) return;
                
                // 显示加载状态
                ordersList.innerHTML = '<tr><td colspan="6" class="table-empty">加载订单数据中...</td></tr>';
                
                // 从服务器获取订单数据
                fetch('/api/get-orders')
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            // 获取订单数据
                            let orders = result.data || [];
                            
                            // 获取搜索关键词
                            const searchTerm = (document.getElementById('orderSearchInput')?.value || '').trim().toLowerCase();
                            
                            // 获取日期筛选
                            const dateFilter = localStorage.getItem('orderDateFilter');
                            
                            // 应用筛选条件
                            if (searchTerm || dateFilter) {
                                orders = orders.filter(order => {
                                    let matchesSearch = true;
                                    let matchesDate = true;
                                    
                                    // 搜索关键词筛选
                                    if (searchTerm) {
                                        matchesSearch = order.id.toLowerCase().includes(searchTerm) || 
                                            (order.username || '').toLowerCase().includes(searchTerm) ||
                                            (order.shippingInfo?.country || '').toLowerCase().includes(searchTerm) ||
                                            order.status.toLowerCase().includes(searchTerm);
                                    }
                                    
                                    // 日期筛选
                                    if (dateFilter) {
                                        const orderDate = new Date(order.date);
                                        const filterDate = new Date(dateFilter);
                                        
                                        // 比较年、月、日是否相同
                                        matchesDate = orderDate.getFullYear() === filterDate.getFullYear() &&
                                            orderDate.getMonth() === filterDate.getMonth() &&
                                            orderDate.getDate() === filterDate.getDate();
                                    }
                                    
                                    // 同时满足搜索和日期筛选条件
                                    return matchesSearch && matchesDate;
                                });
                            }
                            
                            // 获取排序方式
                            const sortNewestFirst = localStorage.getItem('orderSortNewestFirst') !== 'false';
                            
                            // 应用排序
                            orders.sort((a, b) => {
                                const dateA = new Date(a.date || 0).getTime();
                                const dateB = new Date(b.date || 0).getTime();
                                return sortNewestFirst ? (dateB - dateA) : (dateA - dateB);
                            });
                            
                            // 更新排序按钮显示
                            const toggleOrderSortBtn = document.getElementById('toggleOrderSortBtn');
                            if (toggleOrderSortBtn) {
                                const iconElement = toggleOrderSortBtn.querySelector('i');
                                const textElement = toggleOrderSortBtn.querySelector('span');
                                
                                if (sortNewestFirst) {
                                    iconElement.className = 'fas fa-sort-amount-down';
                                    textElement.textContent = '最新优先';
                                } else {
                                    iconElement.className = 'fas fa-sort-amount-up';
                                    textElement.textContent = '最早优先';
                                }
                            }
                            
                            // 如果没有订单数据
                            if (orders.length === 0) {
                                ordersList.innerHTML = '<tr><td colspan="6" class="table-empty">暂无订单数据</td></tr>';
                                document.getElementById('currentOrderPage').textContent = '1';
                                document.getElementById('totalOrderPages').textContent = '1';
                                document.getElementById('prevOrderPage').disabled = true;
                                document.getElementById('nextOrderPage').disabled = true;
                                return;
                            }
                            
                            // 分页处理
                            const pageSize = parseInt(document.getElementById('orderPageSize').value) || 10;
                            const totalPages = Math.ceil(orders.length / pageSize);
                            let currentPage = parseInt(document.getElementById('currentOrderPage').textContent) || 1;
                            
                            // 确保当前页在有效范围内
                            if (currentPage > totalPages) {
                                currentPage = totalPages;
                            }
                            if (currentPage < 1) {
                                currentPage = 1;
                            }
                            
                            // 更新页码信息
                            document.getElementById('currentOrderPage').textContent = currentPage;
                            document.getElementById('totalOrderPages').textContent = totalPages;
                            
                            // 启用或禁用上一页、下一页按钮
                            document.getElementById('prevOrderPage').disabled = (currentPage <= 1);
                            document.getElementById('nextOrderPage').disabled = (currentPage >= totalPages);
                            
                            // 计算当前页的起始和结束索引
                            const startIndex = (currentPage - 1) * pageSize;
                            const endIndex = Math.min(startIndex + pageSize, orders.length);
                            
                            // 清空现有内容
                            ordersList.innerHTML = '';
                            
                            // 只显示当前页的订单
                            for (let i = startIndex; i < endIndex; i++) {
                                const order = orders[i];
                                const row = document.createElement('tr');
                                
                                // 格式化日期
                                const orderDate = new Date(order.date);
                                const formattedDate = `${orderDate.getFullYear()}-${(orderDate.getMonth() + 1).toString().padStart(2, '0')}-${orderDate.getDate().toString().padStart(2, '0')} ${orderDate.getHours().toString().padStart(2, '0')}:${orderDate.getMinutes().toString().padStart(2, '0')}`;
                                
                                // 获取用户名
                                const username = order.username || '未知用户';
                                
                                // 获取收货国家
                                const country = order.shippingInfo?.country || '未指定';
                                
                                row.innerHTML = `
                                    <td>${order.id}</td>
                                    <td>${username}</td>
                                    <td>${country}</td>
                                    <td>${formattedDate}</td>
                                    <td>${order.status}</td>
                                    <td>
                                        <button class="btn-view" data-id="${order.id}">查看</button>
                                        <button class="btn-edit" data-id="${order.id}">编辑</button>
                                    </td>
                                `;
                                
                                ordersList.appendChild(row);
                            }
                            
                            // 添加查看订单详情功能
                            setupOrderActions();
                        } else {
                            // 加载失败
                            ordersList.innerHTML = `<tr><td colspan="6" class="table-empty">加载失败: ${result.message || '未知错误'}</td></tr>`;
                            console.error("Failed to load orders:", result.message);
                        }
                    })
                    .catch(error => {
                        console.error('加载订单数据失败:', error);
                        ordersList.innerHTML = '<tr><td colspan="6" class="table-empty">加载订单数据时发生错误，请刷新重试</td></tr>';
                    });
            };
            
            // 初始化订单分页功能
            function setupOrderPagination() {
                // 处理排序切换按钮
                const toggleOrderSortBtn = document.getElementById('toggleOrderSortBtn');
                if (toggleOrderSortBtn) {
                    toggleOrderSortBtn.addEventListener('click', function() {
                        // 获取当前排序方式并切换
                        const currentSortNewestFirst = localStorage.getItem('orderSortNewestFirst') !== 'false';
                        const newSortNewestFirst = !currentSortNewestFirst;
                        
                        // 保存新的排序方式
                        localStorage.setItem('orderSortNewestFirst', newSortNewestFirst.toString());
                        
                        // 重新加载数据
                        loadOrdersData();
                    });
                }
                
                // 处理搜索输入
                const orderSearchInput = document.getElementById('orderSearchInput');
                if (orderSearchInput) {
                    // 使用防抖函数优化搜索性能
                    let searchTimeout;
                    orderSearchInput.addEventListener('input', function() {
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(() => {
                            // 重置到第一页
                            document.getElementById('currentOrderPage').textContent = '1';
                            // 重新加载带有搜索条件的数据
                            loadOrdersData();
                        }, 300); // 300毫秒防抖
                    });
                }
                
                // 处理页面切换
                const prevPageBtn = document.getElementById('prevOrderPage');
                const nextPageBtn = document.getElementById('nextOrderPage');
                
                if (prevPageBtn) {
                    prevPageBtn.addEventListener('click', function() {
                        if (!this.disabled) {
                            const currentPage = parseInt(document.getElementById('currentOrderPage').textContent);
                            if (currentPage > 1) {
                                document.getElementById('currentOrderPage').textContent = currentPage - 1;
                                loadOrdersData();
                            }
                        }
                    });
                }
                
                if (nextPageBtn) {
                    nextPageBtn.addEventListener('click', function() {
                        if (!this.disabled) {
                            const currentPage = parseInt(document.getElementById('currentOrderPage').textContent);
                            const totalPages = parseInt(document.getElementById('totalOrderPages').textContent);
                            if (currentPage < totalPages) {
                                document.getElementById('currentOrderPage').textContent = currentPage + 1;
                                loadOrdersData();
                            }
                        }
                    });
                }
                
                // 处理每页显示数量变化
                const pageSizeSelect = document.getElementById('orderPageSize');
                if (pageSizeSelect) {
                    pageSizeSelect.addEventListener('change', function() {
                        document.getElementById('currentOrderPage').textContent = '1'; // 切换到第一页
                        loadOrdersData();
                    });
                }
                
                // 处理页面跳转
                const jumpToPageInput = document.getElementById('jumpToOrderPage');
                const jumpBtn = document.getElementById('jumpOrderBtn');
                
                // 处理跳转按钮点击
                if (jumpBtn) {
                    jumpBtn.addEventListener('click', function() {
                        handleJumpToPage();
                    });
                }
                
                // 处理输入框回车键
                if (jumpToPageInput) {
                    jumpToPageInput.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            handleJumpToPage();
                        }
                    });
                }
                
                // 页面跳转处理函数
                function handleJumpToPage() {
                    const jumpToPage = parseInt(jumpToPageInput.value);
                    const totalPages = parseInt(document.getElementById('totalOrderPages').textContent);
                    
                    if (jumpToPage && jumpToPage > 0 && jumpToPage <= totalPages) {
                        document.getElementById('currentOrderPage').textContent = jumpToPage;
                        loadOrdersData();
                        // 清空输入框
                        jumpToPageInput.value = '';
                    } else {
                        // 输入无效
                        showToast('请输入有效的页码', 'warning');
                        jumpToPageInput.focus();
                    }
                }
            }
            
            // 初始化订单日期选择器
            const initOrderDatepicker = async () => {
                // 获取订单日期统计数据
                const orderStats = await fetchOrderStats();
                
                // 初始化日期选择器
                const dateFilterInput = document.getElementById('orderDateFilter');
                if (!dateFilterInput) return;
                
                // 从localStorage获取上次使用的日期
                const savedDateFilter = localStorage.getItem('orderDateFilter');
                
                // 初始化Flatpickr
                const fp = flatpickr(dateFilterInput, {
                    locale: "zh",
                    dateFormat: "Y-m-d",
                    allowInput: false,
                    disableMobile: true,
                    defaultDate: savedDateFilter || null,
                    onReady: function() {
                        // 如果有保存的日期，添加高亮样式
                        if (savedDateFilter) {
                            const dateFilterContainer = document.querySelector('#ordersContent .date-filter');
                            if (dateFilterContainer) {
                                dateFilterContainer.classList.add('date-filter-active');
                            }
                        }
                    },
                    onDayCreate: function(dObj, dStr, fp, dayElem) {
                        // 为了确保一致性，使用与筛选相同的格式化方法
                        const currentDate = dayElem.dateObj;
                        const year = currentDate.getFullYear();
                        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
                        const day = String(currentDate.getDate()).padStart(2, '0');
                        const dateStr = `${year}-${month}-${day}`;
                        
                        // 检查当前日期是否有订单
                        const count = orderStats[dateStr] || 0;
                        
                        // 如果有订单，添加高亮样式和订单数量
                        if (count > 0) {
                            dayElem.classList.add('has-orders');
                            
                            // 创建显示订单数量的元素
                            const countElem = document.createElement('span');
                            countElem.className = 'order-count';
                            countElem.textContent = count;
                            dayElem.appendChild(countElem);
                        }
                    },
                    onChange: function(selectedDates, dateStr) {
                        if (dateStr) {
                            // 重置到第一页
                            document.getElementById('currentOrderPage').textContent = '1';
                            
                            // 添加激活状态样式
                            const dateFilterContainer = document.querySelector('#ordersContent .date-filter');
                            if (dateFilterContainer) {
                                dateFilterContainer.classList.add('date-filter-active');
                            }
                            
                            // 保存到localStorage
                            localStorage.setItem('orderDateFilter', dateStr);
                            
                            // 加载筛选后的数据
                            loadOrdersData();
                        }
                    }
                });
                
                // 清除按钮事件
                const clearDateFilterBtn = document.getElementById('clearOrderDateFilter');
                if (clearDateFilterBtn) {
                    clearDateFilterBtn.addEventListener('click', function() {
                        // 清空日期选择器
                        fp.clear();
                        
                        // 移除激活状态样式
                        const dateFilterContainer = document.querySelector('#ordersContent .date-filter');
                        if (dateFilterContainer) {
                            dateFilterContainer.classList.remove('date-filter-active');
                        }
                        
                        // 从localStorage中移除
                        localStorage.removeItem('orderDateFilter');
                        
                        // 重置到第一页并重新加载数据
                        document.getElementById('currentOrderPage').textContent = '1';
                        loadOrdersData();
                    });
                }
                
                // 应用按钮事件
                const applyDateFilterBtn = document.getElementById('applyOrderDateFilter');
                if (applyDateFilterBtn) {
                    applyDateFilterBtn.addEventListener('click', function() {
                        if (dateFilterInput.value) {
                            // 重置到第一页
                            document.getElementById('currentOrderPage').textContent = '1';
                            // 重新加载数据
                            loadOrdersData();
                        } else {
                            showToast('请选择日期', 'warning');
                        }
                    });
                }
            };
            
            // 设置订单操作事件处理
            function setupOrderActions() {
                // 查看订单详情按钮事件
                document.querySelectorAll('.btn-view').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const orderId = this.getAttribute('data-id');
                        viewOrderDetails(orderId);
                    });
                });
                
                // 编辑订单按钮事件
                document.querySelectorAll('.btn-edit').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const orderId = this.getAttribute('data-id');
                        editOrder(orderId);
                    });
                });
            }
            
            // 查看订单详情功能
            function viewOrderDetails(orderId) {
                // 创建订单详情弹窗
                fetch('/api/get-orders')
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            const orders = result.data || [];
                            const order = orders.find(o => o.id === orderId);
                            
                            if (!order) {
                                alert('找不到该订单信息');
                                return;
                            }
                            
                            // 创建弹窗
                            const modal = document.createElement('div');
                            modal.className = 'modal';
                            modal.style.display = 'flex';
                            
                            // 格式化日期
                            const orderDate = new Date(order.date);
                            const formattedDate = `${orderDate.getFullYear()}-${(orderDate.getMonth() + 1).toString().padStart(2, '0')}-${orderDate.getDate().toString().padStart(2, '0')} ${orderDate.getHours().toString().padStart(2, '0')}:${orderDate.getMinutes().toString().padStart(2, '0')}`;
                            
                            // 运输方式文本映射
                            const shippingMethodText = {
                                'air_regular': '空运普货',
                                'air_sensitive': '空运敏货',
                                'sea_small_regular': '海运小件普货',
                                'sea_small_sensitive': '海运小件敏货',
                                'sea_full_regular': '海运整柜普货',
                                'sea_full_sensitive': '海运整柜敏货',
                                'sea_oversized_regular': '海运超大件普货',
                                'sea_oversized_sensitive': '海运超大件敏货',
                                'rail_full_regular': '铁运整柜普货',
                                'rail_full_sensitive': '铁运整柜敏货',
                                'rail_oversized_regular': '铁运超大件普货',
                                'rail_oversized_sensitive': '铁运超大件敏货',
                                'land_small_regular': '陆运小件普货',
                                'land_small_sensitive': '陆运小件敏货',
                                'land_full_regular': '陆运整柜普货',
                                'land_full_sensitive': '陆运整柜敏货',
                                'land_oversized_regular': '陆运超大件普货',
                                'land_oversized_sensitive': '陆运超大件敏货',
                                'air': '空运',
                                'sea': '海运'
                            };
                            
                            // 创建商品列表HTML
                            let itemsHTML = '';
                            order.items.forEach(item => {
                                const imageHtml = item.imageUrl ? 
                                    `<img src="${item.imageUrl}" alt="商品图片" style="width:80px;height:80px;object-fit:cover;border-radius:4px;">` :
                                    `<div style="width:80px;height:80px;background:#f0f0f0;display:flex;align-items:center;justify-content:center;border-radius:4px;">无图片</div>`;
                                
                                itemsHTML += `
                                    <div style="display:flex;margin-bottom:15px;padding:10px;background:#f9f9f9;border-radius:5px;align-items:center;">
                                        ${imageHtml}
                                        <div style="margin-left:15px;flex:1;">
                                            <div style="font-weight:bold;margin-bottom:5px;">${item.productName || '未命名商品'}</div>
                                            <div style="font-size:14px;color:#666;">
                                                <div>物流码: ${item.code || '无'}</div>
                                                <div>价格: ¥${item.price || '0'}</div>
                                                <div>运输方式: ${shippingMethodText[item.shippingMethod] || item.shippingMethod || '未指定'}</div>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            });
                            
                            // 构建弹窗内容
                            modal.innerHTML = `
                                <div class="modal-content" style="width:80%;max-width:800px;">
                                    <div class="modal-header">
                                        <div style="display:flex; align-items:center;">
                                            <h3 style="margin-right:15px;">订单详情</h3>
                                            <button id="downloadOrderBtn" style="background-color:#28a745; color:white; border:none; border-radius:4px; padding:6px 12px; display:flex; align-items:center; cursor:pointer;">
                                                <i class="fas fa-download" style="margin-right:5px;"></i> 下载订单信息
                                            </button>
                                        </div>
                                        <span class="close-modal" onclick="document.querySelector('.modal').remove();">&times;</span>
                                    </div>
                                    <div class="modal-body" style="max-height:70vh;overflow-y:auto;">
                                        <div style="display:flex;justify-content:space-between;margin-bottom:20px;">
                                            <div>
                                                <div style="font-weight:bold;margin-bottom:5px;">订单号: ${order.id}</div>
                                                <div>用户: ${order.username || '未知'}</div>
                                                <div>邮箱: ${order.userEmail || '未提供'}</div>
                                                <div>下单时间: ${formattedDate}</div>
                                            </div>
                                            <div style="padding:5px 15px;background-color:#f0f8ff;color:#0c4da2;border-radius:4px;font-weight:bold;">
                                                ${order.status}
                                            </div>
                                        </div>
                                        
                                        <h4 style="margin:20px 0 10px;color:#0c4da2;border-bottom:1px solid #eee;padding-bottom:5px;">收货信息</h4>
                                        <div style="margin-bottom:20px;">
                                            <div>收件人: ${order.shippingInfo?.name || '未提供'}</div>
                                            <div>联系电话: ${order.shippingInfo?.phone || '未提供'}</div>
                                            <div>国家: ${order.shippingInfo?.country || '未提供'}</div>
                                            <div>城市: ${order.shippingInfo?.city || '未提供'}</div>
                                            <div>地址: ${order.shippingInfo?.address || '未提供'}</div>
                                            <div>邮编: ${order.shippingInfo?.zipCode || '未提供'}</div>
                                        </div>
                                        
                                        <h4 style="margin:20px 0 10px;color:#0c4da2;border-bottom:1px solid #eee;padding-bottom:5px;">商品信息</h4>
                                        <div>
                                            ${itemsHTML || '<div style="text-align:center;padding:20px;color:#999;">无商品信息</div>'}
                                        </div>
                                    </div>
                                </div>
                            `;
                            
                            // 添加弹窗到页面
                            document.body.appendChild(modal);
                            
                            // 添加下载按钮事件
                            const downloadOrderBtn = document.getElementById('downloadOrderBtn');
                            if (downloadOrderBtn) {
                                downloadOrderBtn.addEventListener('click', function() {
                                    downloadOrderDetails(order);
                                });
                            }
                        }
                    })
                    .catch(error => {
                        console.error('获取订单详情失败:', error);
                        alert('获取订单详情失败，请稍后重试');
                    });
            }
            
            // 下载订单详情为Excel文件
            function downloadOrderDetails(order) {
                try {
                    // 创建一个工作簿对象
                    let csvContent = "数据类型,字段,值\n";
                    
                    // 添加订单基本信息
                    csvContent += `订单信息,订单号,${order.id}\n`;
                    csvContent += `订单信息,下单时间,${new Date(order.date).toLocaleString()}\n`;
                    csvContent += `订单信息,订单状态,${order.status}\n`;
                    csvContent += `订单信息,用户名,${order.username || '未知'}\n`;
                    csvContent += `订单信息,用户邮箱,${order.userEmail || '未提供'}\n`;
                    
                    // 添加收货信息
                    csvContent += `收货信息,收件人,${order.shippingInfo?.name || '未提供'}\n`;
                    csvContent += `收货信息,联系电话,${order.shippingInfo?.phone || '未提供'}\n`;
                    csvContent += `收货信息,国家,${order.shippingInfo?.country || '未提供'}\n`;
                    csvContent += `收货信息,城市,${order.shippingInfo?.city || '未提供'}\n`;
                    csvContent += `收货信息,地址,${order.shippingInfo?.address || '未提供'}\n`;
                    csvContent += `收货信息,邮编,${order.shippingInfo?.zipCode || '未提供'}\n`;
                    
                    // 运输方式文本映射
                    const shippingMethodText = {
                        'air_regular': '空运普货',
                        'air_sensitive': '空运敏货',
                        'sea_small_regular': '海运小件普货',
                        'sea_small_sensitive': '海运小件敏货',
                        'sea_full_regular': '海运整柜普货',
                        'sea_full_sensitive': '海运整柜敏货',
                        'sea_oversized_regular': '海运超大件普货',
                        'sea_oversized_sensitive': '海运超大件敏货',
                        'rail_full_regular': '铁运整柜普货',
                        'rail_full_sensitive': '铁运整柜敏货',
                        'rail_oversized_regular': '铁运超大件普货',
                        'rail_oversized_sensitive': '铁运超大件敏货',
                        'land_small_regular': '陆运小件普货',
                        'land_small_sensitive': '陆运小件敏货',
                        'land_full_regular': '陆运整柜普货',
                        'land_full_sensitive': '陆运整柜敏货',
                        'land_oversized_regular': '陆运超大件普货',
                        'land_oversized_sensitive': '陆运超大件敏货',
                        'air': '空运',
                        'sea': '海运'
                    };
                    
                    // 添加商品信息
                    if (order.items && order.items.length > 0) {
                        order.items.forEach((item, index) => {
                            csvContent += `商品${index + 1},物流码,${item.code || '未提供'}\n`;
                            csvContent += `商品${index + 1},商品名称,${item.productName || '未提供'}\n`;
                            csvContent += `商品${index + 1},价格,${item.price || '未提供'}\n`;
                            csvContent += `商品${index + 1},运输方式,${shippingMethodText[item.shippingMethod] || item.shippingMethod || '未提供'}\n`;
                            csvContent += `商品${index + 1},图片链接,${item.imageUrl || '无图片'}\n`;
                        });
                    } else {
                        csvContent += `商品信息,无商品数据,无\n`;
                    }
                    
                    // 创建Blob对象
                    const blob = new Blob(["\ufeff" + csvContent], { type: 'text/csv;charset=utf-8;' });
                    
                    // 创建下载链接
                    const link = document.createElement('a');
                    const url = URL.createObjectURL(blob);
                    
                    // 设置下载属性
                    link.setAttribute('href', url);
                    link.setAttribute('download', `订单${order.id}_${new Date().toISOString().split('T')[0]}.csv`);
                    link.style.display = 'none';
                    
                    // 添加到DOM并触发点击
                    document.body.appendChild(link);
                    link.click();
                    
                    // 清理
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                    
                    showToast('订单信息已下载', 'success');
                } catch (error) {
                    console.error('下载订单详情失败:', error);
                    showToast('下载订单详情失败，请稍后重试', 'error');
                }
            }
            
            // 编辑订单功能
            function editOrder(orderId) {
                // 获取订单数据以显示当前状态
                fetch('/api/get-orders')
                    .then(response => response.json())
                    .then(result => {
                        if (!result.success) {
                            alert('获取订单数据失败');
                            return;
                        }

                        const orders = result.data || [];
                        const order = orders.find(o => o.id === orderId);
                        
                        if (!order) {
                            alert('找不到该订单信息');
                            return;
                        }
                        
                        // 创建弹窗
                        const modal = document.createElement('div');
                        modal.className = 'modal';
                        modal.style.display = 'flex';
                        
                        // 可选的订单状态
                        const statusOptions = [
                            '待处理', '处理中', '已发货', '已送达', '已完成', '已取消', '待付款'
                        ];
                        
                        // 生成状态选择选项
                        let statusOptionsHTML = '';
                        statusOptions.forEach(status => {
                            const selected = status === order.status ? 'selected' : '';
                            statusOptionsHTML += `<option value="${status}" ${selected}>${status}</option>`;
                        });
                        
                        // 构建弹窗内容
                        modal.innerHTML = `
                            <div class="modal-content" style="width:500px;">
                                <div class="modal-header">
                                    <h3>编辑订单</h3>
                                    <span class="close-modal" onclick="document.querySelector('.modal').remove();">&times;</span>
                                </div>
                                <div class="modal-body">
                                    <div style="margin-bottom:20px;">
                                        <div style="font-weight:bold;">订单号: ${order.id}</div>
                                        <div>用户: ${order.username || '未知用户'}</div>
                                    </div>
                                    
                                    <div style="margin-bottom:20px;">
                                        <label style="display:block;margin-bottom:8px;font-weight:bold;">订单状态:</label>
                                        <select id="orderStatus" style="width:100%;padding:8px;border-radius:4px;border:1px solid #ddd;">
                                            ${statusOptionsHTML}
                                        </select>
                                    </div>
                                    
                                    <div style="display:flex;justify-content:flex-end;gap:10px;margin-top:20px;">
                                        <button onclick="document.querySelector('.modal').remove();" style="padding:8px 15px;background:#f5f5f5;border:none;border-radius:4px;cursor:pointer;">取消</button>
                                        <button onclick="updateOrderStatus('${orderId}')" style="padding:8px 15px;background:#0c4da2;color:white;border:none;border-radius:4px;cursor:pointer;">保存</button>
                                    </div>
                                </div>
                            </div>
                        `;
                        
                        // 添加弹窗到页面
                        document.body.appendChild(modal);
                    })
                    .catch(error => {
                        console.error('获取订单数据失败:', error);
                        alert('获取订单数据失败，请稍后重试');
                    });
            }
            
            // 更新订单状态
            function updateOrderStatus(orderId) {
                const statusSelect = document.getElementById('orderStatus');
                if (!statusSelect) {
                    alert('状态选择器未找到，请刷新页面重试');
                    return;
                }
                
                const status = statusSelect.value;
                
                // 发送请求更新状态
                fetch('/api/update-order-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        orderId: orderId,
                        status: status
                    })
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        // 关闭弹窗
                        document.querySelector('.modal').remove();
                        // 显示成功提示
                        showToast('订单状态已更新', 'success');
                        // 重新加载订单数据
                        loadOrdersData();
                    } else {
                        alert(`更新失败: ${result.message || '未知错误'}`);
                    }
                })
                .catch(error => {
                    console.error('更新订单状态失败:', error);
                    alert('更新订单状态失败，请稍后重试');
                });
            }
            
            // 加载用户管理数据
            const loadUsersData = () => {
                // 获取用户统计数据
                fetch('/api/users-stats')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            document.getElementById('totalUsers').textContent = data.totalUsers || 0;
                            document.getElementById('newUsers').textContent = data.newUsers || 0;
                            document.getElementById('blacklistedUsers').textContent = data.blacklistedUsers || 0;
                        }
                    })
                    .catch(error => {
                        console.error('获取用户统计数据失败:', error);
                        showToast('获取用户统计数据失败', 'error');
                    });
                
                // 获取搜索关键词
                const searchTerm = (document.getElementById('userSearchInput')?.value || '').trim().toLowerCase();
                
                // 获取日期筛选值
                const dateFilter = document.getElementById('dateFilter')?.value;
                
                // 获取注册天数筛选值
                const daysFilter = document.getElementById('daysFilter')?.value.trim();
                
                // 保存日期筛选值到本地存储
                if (dateFilter) {
                    localStorage.setItem('userDateFilter', dateFilter);
                } else {
                    localStorage.removeItem('userDateFilter');
                }
                
                // 获取用户列表数据
                fetch('/api/users')
                    .then(response => response.json())
                    .then(async data => {
                        const userTableBody = document.getElementById('userTableBody');
                        if (!userTableBody) return;
                        
                        if (!data.success) {
                            userTableBody.innerHTML = `<tr><td colspan="6" class="table-empty">加载失败: ${data.message}</td></tr>`;
                            return;
                        }
                        
                        let users = data.users;
                        
                        // 获取商品管理列表数据以标记商品商家
                        try {
                            const merchantAuthResponse = await fetch('/api/merchant-auth-list');
                            const merchantAuthData = await merchantAuthResponse.json();
                            
                            if (merchantAuthData.success && merchantAuthData.merchants) {
                                // 找出所有已认证的商家
                                const approvedMerchants = merchantAuthData.merchants
                                    .filter(merchant => merchant.status === 'approved')
                                    .map(merchant => merchant.username);
                                
                                // 为用户添加商品管理状态标志
                                users = users.map(user => ({
                                    ...user,
                                    isMerchantVerified: approvedMerchants.includes(user.username)
                                }));
                            }
                        } catch (error) {
                            console.error('获取商品管理数据失败:', error);
                        }
                        
                        if (!users || users.length === 0) {
                            userTableBody.innerHTML = '<tr><td colspan="6" class="table-empty">暂无用户数据</td></tr>';
                            return;
                        }
                        
                        // 注册天数筛选
                        if (daysFilter) {
                            const targetDays = parseInt(daysFilter);
                            if (!isNaN(targetDays)) {
                                // 获取当前日期
                                const now = new Date();
                                
                                // 筛选出注册天数符合条件的用户
                                const filteredUsers = users.filter(user => {
                                    if (!user.registrationTime) return false;
                                    
                                    // 计算注册天数
                                    const registerDate = new Date(user.registrationTime);
                                    const daysDiff = Math.floor((now - registerDate) / (1000 * 60 * 60 * 24));
                                    
                                    return daysDiff === targetDays;
                                });
                                
                                users = filteredUsers;
                                
                                // 如果没有匹配的结果
                                if (users.length === 0) {
                                    userTableBody.innerHTML = `<tr><td colspan="6" class="table-empty">没有找到注册天数为 ${targetDays} 天的用户</td></tr>`;
                                    return;
                                }
                                
                                console.log(`注册天数 ${targetDays} 筛选结果: 找到 ${users.length} 个用户`);
                            }
                        }
                        // 日期筛选
                        else if (dateFilter) {
                            const filteredUsers = [];
                            
                            // 遍历用户并进行筛选
                            users.forEach(user => {
                                if (!user.registrationTime) return;
                                
                                // 将注册日期转换为YYYY-MM-DD格式进行比较
                                const userRegDate = new Date(user.registrationTime);
                                const userYear = userRegDate.getFullYear();
                                const userMonth = String(userRegDate.getMonth() + 1).padStart(2, '0');
                                const userDay = String(userRegDate.getDate()).padStart(2, '0');
                                const formattedUserDate = `${userYear}-${userMonth}-${userDay}`;
                                
                                // 调试信息
                                console.log(`比较日期 - 筛选: ${dateFilter}, 用户: ${formattedUserDate}, 原始: ${user.registrationTime}`);
                                
                                if (formattedUserDate === dateFilter) {
                                    filteredUsers.push(user);
                                }
                            });
                            
                            users = filteredUsers;
                            
                            // 如果没有匹配的结果
                            if (users.length === 0) {
                                userTableBody.innerHTML = `<tr><td colspan="6" class="table-empty">没有找到注册日期为 ${dateFilter} 的用户</td></tr>`;
                                return;
                            }
                            
                            // 调试信息
                            console.log(`日期 ${dateFilter} 筛选结果: 找到 ${users.length} 个用户`);
                        }
                        
                        // 搜索过滤
                        if (searchTerm) {
                            users = users.filter(user => {
                                // 在用户ID中搜索
                                if (user.id && user.id.toString().toLowerCase().includes(searchTerm)) {
                                    return true;
                                }
                                
                                // 在用户名中搜索
                                if (user.username && user.username.toLowerCase().includes(searchTerm)) {
                                    return true;
                                }
                                
                                // 在邮箱中搜索
                                if (user.email && user.email.toLowerCase().includes(searchTerm)) {
                                    return true;
                                }
                                
                                // 在微信账号中搜索
                                if (user.wechat && user.wechat.toLowerCase().includes(searchTerm)) {
                                    return true;
                                }
                                
                                return false;
                            });
                            
                            // 如果没有匹配的结果
                            if (users.length === 0) {
                                userTableBody.innerHTML = `<tr><td colspan="6" class="table-empty">没有找到匹配 "${searchTerm}" 的用户</td></tr>`;
                                return;
                            }
                        }
                        
                        // 获取当前排序方式
                        const sortNewestFirst = localStorage.getItem('userSortNewestFirst') !== 'false';
                        
                        // 按注册时间排序
                        users.sort((a, b) => {
                            const dateA = new Date(a.registrationTime);
                            const dateB = new Date(b.registrationTime);
                            // 根据排序方式决定顺序
                            return sortNewestFirst ? (dateB - dateA) : (dateA - dateB);
                        });
                        
                        // 更新排序按钮显示
                        const toggleSortBtn = document.getElementById('toggleSortBtn');
                        if (toggleSortBtn) {
                            const iconElement = toggleSortBtn.querySelector('i');
                            const textElement = toggleSortBtn.querySelector('span');
                            
                            if (sortNewestFirst) {
                                iconElement.className = 'fas fa-sort-amount-down';
                                textElement.textContent = '最新优先';
                            } else {
                                iconElement.className = 'fas fa-sort-amount-up';
                                textElement.textContent = '最早优先';
                            }
                        }
                        
                        // 清空现有内容
                        userTableBody.innerHTML = '';
                        
                        // 分页处理
                        const pageSize = parseInt(document.getElementById('pageSize').value) || 10;
                        const totalPages = Math.ceil(users.length / pageSize);
                        let currentPage = parseInt(document.getElementById('currentPage').textContent) || 1;
                        
                        // 确保当前页在有效范围内
                        if (currentPage > totalPages) {
                            currentPage = totalPages;
                        }
                        
                        // 更新页码信息
                        document.getElementById('currentPage').textContent = currentPage;
                        document.getElementById('totalPages').textContent = totalPages;
                        
                        // 启用或禁用上一页、下一页按钮
                        const prevPageBtn = document.getElementById('prevPage');
                        const nextPageBtn = document.getElementById('nextPage');
                        
                        if (prevPageBtn) {
                            if (currentPage <= 1) {
                                prevPageBtn.disabled = true;
                            } else {
                                prevPageBtn.disabled = false;
                            }
                        }
                        
                        if (nextPageBtn) {
                            if (currentPage >= totalPages) {
                                nextPageBtn.disabled = true;
                            } else {
                                nextPageBtn.disabled = false;
                            }
                        }
                        
                        // 当前日期，用于计算注册天数
                        const now = new Date();
                        
                        // 计算当前页的起始和结束索引
                        const startIndex = (currentPage - 1) * pageSize;
                        const endIndex = Math.min(startIndex + pageSize, users.length);
                        
                        // 仅显示当前页的用户
                        for (let i = startIndex; i < endIndex; i++) {
                            const user = users[i];
                            const row = document.createElement('tr');
                            
                            // 计算注册天数
                            const registerDate = new Date(user.registrationTime);
                            const daysDiff = Math.floor((now - registerDate) / (1000 * 60 * 60 * 24));
                            
                            // 格式化日期为 YYYY-MM-DD
                            const formattedDate = registerDate.toISOString().split('T')[0];
                            
                            // 确定用户联系方式（邮箱或微信）
                            const contactInfo = user.email ? user.email : (user.wechat ? user.wechat : '未提供');
                            
                            // 不再显示商家认证标识
                            // const merchantBadge = user.isMerchantVerified ? '<i class="fas fa-check-circle" style="color:#52c41a;margin-right:5px;" title="已认证商家"></i>' : '';
                            
                            row.innerHTML = `
                                <td>${user.id}</td>
                                <td>${user.username}</td>
                                <td>${contactInfo}</td>
                                <td>${formattedDate}</td>
                                <td>${daysDiff}天</td>
                                <td>
                                    <button class="btn-view" data-id="${user.id}">查看</button>
                                    <button class="btn-blacklist" data-id="${user.id}">${user.blacklisted ? '解除拉黑' : '拉黑'}</button>
                                </td>
                            `;
                            
                            userTableBody.appendChild(row);
                        }
                        
                        // 添加事件监听
                        setupUserActionButtons();
                    })
                    .catch(error => {
                        console.error('获取用户数据失败:', error);
                        const userTableBody = document.getElementById('userTableBody');
                        if (userTableBody) {
                            userTableBody.innerHTML = '<tr><td colspan="6" class="table-empty">加载用户数据失败，请刷新重试</td></tr>';
                        }
                        showToast('获取用户数据失败', 'error');
                    });
            };
            
            // 加载黑名单用户
            const loadBlacklistedUsers = () => {
                fetch('/api/users?blacklisted=true')
                    .then(response => response.json())
                    .then(async data => {
                        const blacklistedUserTableBody = document.getElementById('blacklistedUserTableBody');
                        if (!blacklistedUserTableBody) return;
                        
                        if (!data.success) {
                            blacklistedUserTableBody.innerHTML = `<tr><td colspan="5" class="table-empty">加载失败: ${data.message}</td></tr>`;
                            return;
                        }
                        
                        let users = data.users;
                        
                        // 获取商品管理列表数据以标记商品商家
                        try {
                            const merchantAuthResponse = await fetch('/api/merchant-auth-list');
                            const merchantAuthData = await merchantAuthResponse.json();
                            
                            if (merchantAuthData.success && merchantAuthData.merchants) {
                                // 找出所有已认证的商家
                                const approvedMerchants = merchantAuthData.merchants
                                    .filter(merchant => merchant.status === 'approved')
                                    .map(merchant => merchant.username);
                                
                                // 为用户添加商品管理状态标志
                                users = users.map(user => ({
                                    ...user,
                                    isMerchantVerified: approvedMerchants.includes(user.username)
                                }));
                            }
                        } catch (error) {
                            console.error('获取商品管理数据失败:', error);
                        }
                        
                        if (!users || users.length === 0) {
                            blacklistedUserTableBody.innerHTML = '<tr><td colspan="5" class="table-empty">暂无黑名单用户</td></tr>';
                            return;
                        }
                        
                        // 清空现有内容
                        blacklistedUserTableBody.innerHTML = '';
                        
                        // 当前日期，用于计算注册天数
                        const now = new Date();
                        
                        // 添加用户行
                        users.forEach(user => {
                            const row = document.createElement('tr');
                            
                            // 格式化注册日期为 YYYY-MM-DD
                            const registerDate = new Date(user.registrationTime);
                            const formattedRegDate = registerDate.toISOString().split('T')[0];
                            
                            // 格式化拉黑日期为 YYYY-MM-DD
                            let formattedBlacklistDate = '-';
                            if (user.blacklistedTime) {
                                const blacklistDate = new Date(user.blacklistedTime);
                                formattedBlacklistDate = blacklistDate.toISOString().split('T')[0];
                            }
                            
                            // 确定用户联系方式（邮箱或微信）
                            const contactInfo = user.email ? user.email : (user.wechat ? user.wechat : '未提供');
                            
                            // 不再显示商家认证标识
                            // const merchantBadge = user.isMerchantVerified ? '<i class="fas fa-check-circle" style="color:#52c41a;margin-right:5px;" title="已认证商家"></i>' : '';
                            
                            row.innerHTML = `
                                <td>${user.id}</td>
                                <td>${user.username}</td>
                                <td>${contactInfo}</td>
                                <td>${formattedRegDate}</td>
                                <td>${formattedBlacklistDate}</td>
                                <td>
                                    <button class="btn-view" data-id="${user.id}">查看</button>
                                    <button class="btn-unblacklist" data-id="${user.id}">解除拉黑</button>
                                </td>
                            `;
                            
                            blacklistedUserTableBody.appendChild(row);
                        });
                        
                        // 添加解除拉黑按钮事件
                        document.querySelectorAll('.btn-unblacklist').forEach(btn => {
                            btn.addEventListener('click', function() {
                                const userId = this.getAttribute('data-id');
                                
                                if (confirm(`确定要解除拉黑用户 ${userId} 吗？`)) {
                                    toggleUserBlacklist(userId, false, () => {
                                        // 成功后关闭模态框并刷新用户列表
                                        document.getElementById('blacklistModal').style.display = 'none';
                                        loadUsersData();
                                    });
                                }
                            });
                        });
                    })
                    .catch(error => {
                        console.error('获取黑名单用户数据失败:', error);
                        const blacklistedUserTableBody = document.getElementById('blacklistedUserTableBody');
                        if (blacklistedUserTableBody) {
                            blacklistedUserTableBody.innerHTML = '<tr><td colspan="5" class="table-empty">加载黑名单用户数据失败，请刷新重试</td></tr>';
                        }
                        showToast('获取黑名单用户数据失败', 'error');
                    });
            };
            
            // 设置用户操作按钮事件监听
            const setupUserActionButtons = () => {
                // 查看用户按钮
                document.querySelectorAll('.btn-view').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const userId = this.getAttribute('data-id');
                        showToast(`查看用户: ${userId}`, 'info');
                        // 实际应用中，这里可以打开用户详情弹窗
                    });
                });
                
                // 拉黑/解除拉黑按钮
                document.querySelectorAll('.btn-blacklist').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const userId = this.getAttribute('data-id');
                        const isBlacklisted = this.textContent === '解除拉黑';
                        const actionText = isBlacklisted ? '解除拉黑' : '拉黑';
                        
                        if (confirm(`确定要${actionText}用户 ${userId} 吗？`)) {
                            toggleUserBlacklist(userId, !isBlacklisted);
                        }
                    });
                });
            };
            
            // 删除用户
            const deleteUser = (userId) => {
                fetch(`/api/users/${userId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('用户删除成功', 'success');
                        loadUsersData(); // 重新加载用户数据
                    } else {
                        showToast(`删除失败: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    console.error('删除用户失败:', error);
                    showToast('删除用户失败，请稍后重试', 'error');
                });
            };
            
            // 拉黑/解除拉黑用户
            const toggleUserBlacklist = (userId, blacklist, callback) => {
                fetch(`/api/users/${userId}/blacklist`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ blacklisted: blacklist })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const actionText = blacklist ? '拉黑' : '解除拉黑';
                        showToast(`用户${actionText}成功`, 'success');
                        
                        // 重新加载用户数据
                        loadUsersData();
                        
                        // 如果提供了回调函数，则执行
                        if (callback && typeof callback === 'function') {
                            callback();
                        }
                    } else {
                        showToast(`操作失败: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    console.error('操作失败:', error);
                    showToast('操作失败，请稍后重试', 'error');
                });
            };
            
                                    // 加载商品管理数据
            const loadMerchantsData = () => {
                // 设置发布商品按钮事件
                document.getElementById('publishProduct').addEventListener('click', () => {
                    // 跳转到商品发布页面
                    window.location.href = 'publish-product.html';
                });

                // 设置添加关键词按钮事件
                document.getElementById('addKeywords').addEventListener('click', () => {
                    // 跳转到关键词管理页面
                    window.location.href = 'keywords-management.html';
                });

                // 设置商品分类按钮事件
                document.getElementById('manageCategories').addEventListener('click', () => {
                    // 检查当前登录状态
                    const adminData = JSON.parse(sessionStorage.getItem('loggedInAdmin') || '{}');

                    if (!adminData.isAdmin) {
                        // 如果未登录，跳转到登录页面
                        window.location.href = 'admin-login.html';
                        return;
                    }

                    // 跳转到商品分类管理页面，带上来源参数
                    window.location.href = 'product-categories.html?from=dashboard';
                });

                // 设置轮播图管理按钮事件
                document.getElementById('manageCarousels').addEventListener('click', () => {
                    // 检查当前登录状态
                    const adminData = JSON.parse(sessionStorage.getItem('loggedInAdmin') || '{}');

                    if (!adminData.isAdmin) {
                        // 如果未登录，跳转到登录页面
                        window.location.href = 'admin-login.html';
                        return;
                    }

                    // 跳转到轮播图管理页面
                    window.location.href = 'carousel-management.html';
                });

                // 加载已发布的商品
                loadPublishedProducts();
            };

            // 加载已发布商品
            const loadPublishedProducts = () => {
                const productsTable = document.getElementById('productsTable');
                const noPublishedProducts = document.getElementById('noPublishedProducts');
                const productsLoading = document.getElementById('productsLoading');

                // 显示加载状态
                productsLoading.style.display = 'block';
                productsTable.style.display = 'none';
                noPublishedProducts.style.display = 'none';

                // 从服务器获取商品列表
                fetch('/api/products')
                .then(response => response.json())
                .then(data => {
                    productsLoading.style.display = 'none';

                    if (data.success) {
                        const products = data.products || [];

                        if (products.length === 0) {
                            noPublishedProducts.style.display = 'block';
                        } else {
                            productsTable.style.display = 'table';
                            renderPublishedProducts(products);
                        }
                    } else {
                        noPublishedProducts.style.display = 'block';
                        document.getElementById('noPublishedProducts').innerHTML = `
                            <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 15px; color: #e74c3c;"></i>
                            <p>加载商品失败: ${data.message || '未知错误'}</p>
                            <button onclick="loadPublishedProducts()" style="margin-top: 15px; background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                                <i class="fas fa-redo"></i> 重新加载
                            </button>
                        `;
                    }
                })
                .catch(error => {
                    console.error('加载商品失败:', error);
                    productsLoading.style.display = 'none';
                    noPublishedProducts.style.display = 'block';
                    document.getElementById('noPublishedProducts').innerHTML = `
                        <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 15px; color: #e74c3c;"></i>
                        <p>网络错误，请检查网络连接</p>
                        <button onclick="loadPublishedProducts()" style="margin-top: 15px; background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                            <i class="fas fa-redo"></i> 重新加载
                        </button>
                    `;
                });
            };

            // 全局变量存储已发布商品数据
            let allPublishedProducts = [];

            // 渲染已发布商品
            const renderPublishedProducts = (products) => {
                allPublishedProducts = products;
                const productsTableBody = document.getElementById('productsTableBody');
                const paginationContainer = document.getElementById('publishedProductsPagination');

                productsTableBody.innerHTML = '';

                // 获取搜索关键词
                const searchInput = document.getElementById('publishedProductsSearchInput');
                const searchKeyword = searchInput ? searchInput.value.trim().toLowerCase() : '';

                // 过滤商品数据
                let filteredProducts = products;
                if (searchKeyword) {
                    filteredProducts = products.filter(product => {
                        return product.name.toLowerCase().includes(searchKeyword) ||
                               product.id.toLowerCase().includes(searchKeyword) ||
                               (product.price && product.price.toString().includes(searchKeyword));
                    });
                }

                if (filteredProducts.length === 0) {
                    paginationContainer.style.display = 'none';
                    if (searchKeyword) {
                        // 显示搜索无结果的提示
                        productsTableBody.innerHTML = `
                            <tr>
                                <td colspan="8" style="text-align: center; padding: 40px; color: #666;">
                                    <i class="fas fa-search" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                                    <p>未找到匹配"${searchKeyword}"的商品</p>
                                </td>
                            </tr>
                        `;
                        document.getElementById('productsTable').style.display = 'table';
                    }
                    return;
                }

                // 分页处理
                const pageSize = parseInt(document.getElementById('publishedPageSize').value) || 10;
                const totalPages = Math.ceil(filteredProducts.length / pageSize);
                let currentPage = parseInt(document.getElementById('currentPublishedPage').textContent) || 1;

                // 确保当前页在有效范围内
                if (currentPage > totalPages) {
                    currentPage = totalPages;
                    document.getElementById('currentPublishedPage').textContent = currentPage;
                }
                if (currentPage < 1) {
                    currentPage = 1;
                    document.getElementById('currentPublishedPage').textContent = currentPage;
                }

                // 计算当前页的数据范围
                const startIndex = (currentPage - 1) * pageSize;
                const endIndex = Math.min(startIndex + pageSize, filteredProducts.length);

                // 只显示当前页的商品
                for (let i = startIndex; i < endIndex; i++) {
                    const product = filteredProducts[i];
                    const productRow = createProductTableRow(product);
                    productsTableBody.appendChild(productRow);
                }

                // 更新分页信息
                document.getElementById('currentPublishedPage').textContent = currentPage;
                document.getElementById('totalPublishedPages').textContent = totalPages;

                // 更新分页按钮状态
                document.getElementById('prevPublishedPage').disabled = currentPage <= 1;
                document.getElementById('nextPublishedPage').disabled = currentPage >= totalPages;

                // 显示分页控制
                paginationContainer.style.display = 'flex';
            };

            // 创建商品表格行
            const createProductTableRow = (product) => {
                const row = document.createElement('tr');
                row.setAttribute('data-product-id', product.id);

                // 格式化发布时间
                const publishDate = new Date(product.publishedAt).toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                });
                const publishTime = new Date(product.publishedAt).toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                // 生成随机销量数据（实际项目中应该从数据库获取）
                const totalSales = Math.floor(Math.random() * 1000);
                const monthlySales = Math.floor(Math.random() * 100);
                const stock = product.stock || Math.floor(Math.random() * 999999);

                row.innerHTML = `
                    <td class="product-image-cell">
                        ${product.mainImage ?
                            `<img src="${product.mainImage}" alt="${product.name}" class="product-image-thumb" onclick="viewProductDetail('${product.id}')" onerror="this.style.display='none'">` :
                            '<div class="product-image-placeholder"><i class="fas fa-image"></i></div>'
                        }
                    </td>
                    <td class="product-name-cell">
                        <a href="javascript:void(0)" class="product-name-link" onclick="viewProductDetail('${product.id}')" title="${product.name}">
                            ${product.name}
                        </a>
                        <div class="product-id">ID:${product.id}</div>
                        <span class="status-tag ${product.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${product.status === 'active' ? '出售中' : '下架'}
                        </span>
                    </td>
                    <td class="product-price-cell">${product.priceDisplay || (product.price ? `¥${parseFloat(product.price).toFixed(2)}` : '¥0.00')}</td>
                    <td class="product-stock-cell">
                        <span class="stock-value ${stock < 10 ? 'stock-low' : stock === 0 ? 'stock-out' : ''}">${stock}</span>
                    </td>
                    <td class="product-sales-cell">${totalSales}</td>
                    <td class="product-sales-cell">${monthlySales}</td>
                    <td class="product-time-cell">
                        ${publishDate}<br>
                        ${publishTime}
                    </td>
                    <td>
                        <div class="product-actions">
                            <button class="action-btn btn-edit" onclick="editProduct('${product.id}')" title="编辑商品">
                                编辑商品
                            </button>
                            <button class="action-btn btn-delete" onclick="deleteProduct('${product.id}')" title="删除商品">
                                删除
                            </button>
                        </div>
                    </td>
                `;

                return row;
            };

            // 查看商品详情
            window.viewProductDetail = (productId) => {
                window.open(`product-detail.html?id=${productId}&from=admin`, '_blank');
            };

            // 编辑商品
            window.editProduct = (productId) => {
                // 这里可以实现编辑功能，暂时跳转到发布页面
                window.location.href = `publish-product.html?edit=${productId}`;
            };

            // 删除商品
            window.deleteProduct = (productId) => {
                if (confirm('确定要删除这个商品吗？此操作不可撤销。')) {
                    // 显示删除中状态
                    const deleteBtn = document.querySelector(`button[onclick="deleteProduct('${productId}')"]`);
                    const originalText = deleteBtn.textContent;
                    deleteBtn.textContent = '删除中...';
                    deleteBtn.disabled = true;

                    // 发送删除请求到服务器
                    fetch(`/api/products/${productId}`, {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showToast('商品删除成功', 'success');
                            // 从表格中移除该行
                            const row = document.querySelector(`tr[data-product-id="${productId}"]`);
                            if (row) {
                                row.remove();
                            }
                            // 检查是否还有商品，如果没有则显示空状态
                            const remainingRows = document.querySelectorAll('#productsTableBody tr');
                            if (remainingRows.length === 0) {
                                document.getElementById('productsTable').style.display = 'none';
                                document.getElementById('noPublishedProducts').style.display = 'block';
                            }
                        } else {
                            throw new Error(data.message || '删除失败');
                        }
                    })
                    .catch(error => {
                        console.error('删除商品失败:', error);
                        showToast('删除失败：' + error.message, 'error');
                        // 恢复按钮状态
                        deleteBtn.textContent = originalText;
                        deleteBtn.disabled = false;
                    });
                }
            };



            // 使loadPublishedProducts函数全局可用
            window.loadPublishedProducts = loadPublishedProducts;

                        // 设置商品管理操作按钮事件处理
            const setupMerchantActionButtons = () => {
                console.log('商品管理操作按钮功能已被移至新页面');
            };
            
            // 设置商品管理分页控制
            const setupMerchantPagination = () => {
                console.log('商品管理分页控制功能已被移至新页面');
            };
            
            // 查看商家详情（已移除）
            const viewMerchantDetails = (merchantId, showActions = true) => {
                console.log('商家详情查看功能已被移除');
            };
            
            // 更新商品状态
            const updateMerchantStatus = (merchantId, status, reason = '') => {
                console.log('更新商品状态功能已被移至新页面');
            };
            
            // 更新商品统计信息
            const updateMerchantStats = async (merchants) => {
                // 该功能已被移至新页面
                console.log('商品统计功能已被移至新页面');
            };
            
            // 获取已拒绝商家列表（已移除）
            const fetchRejectedMerchants = async () => {
                console.log('获取已拒绝商家列表功能已被移除');
                return [];
            };
            
            // 设置商品统计卡片点击事件
            const setupMerchantStatsCardEvents = () => {
                // 该功能已被移至新页面
                console.log('商品统计卡片点击事件功能已被移至新页面');
            };
            
            // 显示商家列表模态框（已移除）
            const showMerchantListModal = (title, merchants, status) => {
                console.log('商家列表模态框功能已被移除');
                
                // 以下代码已被移除
                const statusStyles = {
                    approved: {
                        bgColor: '#f6ffed',
                        textColor: '#52c41a',
                        statusText: '已认证',
                        dateLabel: '认证日期'
                    },
                    pending: {
                        bgColor: '#fffbe6',
                        textColor: '#faad14',
                        statusText: '待审核',
                        dateLabel: '提交日期'
                    },
                    rejected: {
                        bgColor: '#fff2f0',
                        textColor: '#ff4d4f',
                        statusText: '已拒绝',
                        dateLabel: '拒绝日期'
                    }
                };
                
                const style = statusStyles[status] || statusStyles.pending;
                
                // 初始化分页数据
                const pageSize = 10; // 每页显示10条记录
                let currentPage = 1;
                const totalPages = Math.ceil(merchants.length / pageSize);
                
                // 原始商家数据的副本，用于搜索和筛选
                let filteredMerchants = [...merchants];
                
                // 获取当前排序方式，默认最新优先
                let sortNewestFirst = true;
                
                // 创建渲染表格的函数，方便分页时重新渲染
                const renderTable = (page) => {
                    // 计算当前页的数据范围
                    const startIndex = (page - 1) * pageSize;
                    const endIndex = Math.min(startIndex + pageSize, filteredMerchants.length);
                    const currentPageData = filteredMerchants.slice(startIndex, endIndex);
                    
                    // 构建表格HTML
                    let tableRows = '';
                    currentPageData.forEach(merchant => {
                        // 决定显示哪个日期：已认证/已拒绝显示审核日期，待审核显示提交日期
                        let dateToShow;
                        let formattedDate;
                        
                        if (status === 'rejected' && merchant.rejectedTime) {
                            // 对于专门的拒绝记录API返回的数据，使用rejectedTime
                            dateToShow = new Date(merchant.rejectedTime);
                        } else if ((status === 'approved' || status === 'rejected') && merchant.reviewTime) {
                            // 对于已认证或已拒绝状态，优先显示审核日期
                            dateToShow = new Date(merchant.reviewTime);
                        } else if (status === 'rejected' && merchant.lastRejectedTime) {
                            // 兼容旧数据格式
                            dateToShow = new Date(merchant.lastRejectedTime);
                        } else {
                            // 其他情况显示提交日期
                            dateToShow = new Date(merchant.submitTime);
                        }
                        
                        formattedDate = dateToShow.toISOString().split('T')[0]; // YYYY-MM-DD 格式
                        
                        // 格式化审核日期（如果有）
                        let reviewInfo = '';
                        if (merchant.reviewTime) {
                            const reviewDate = new Date(merchant.reviewTime);
                            const formattedReviewDate = reviewDate.toISOString().split('T')[0];
                            reviewInfo = `<p><strong>审核时间:</strong> ${formattedReviewDate}</p>`;
                        }
                        
                        if (merchant.reviewComment) {
                            reviewInfo += `<p><strong>审核备注:</strong> ${merchant.reviewComment}</p>`;
                        }
                        
                        // 确保申请次数字段存在
                        if (status === 'rejected' && !merchant.attemptCount) {
                            merchant.attemptCount = 1;
                        }
                        
                        // 添加申请次数显示（仅在已拒绝列表中显示）
                        const attemptCount = merchant.attemptCount || 1;
                        const attemptBadge = status === 'rejected' ? 
                            `<span style="background-color: #ff7a45; color: white; font-size: 12px; padding: 2px 6px; border-radius: 10px; margin-right: 6px;">${attemptCount}</span>` : '';
                        
                        tableRows += `
                            <tr>
                                <td>${attemptBadge}${merchant.username || merchant.id || '未分配'}</td>
                                <td>${merchant.companyName || '未填写'}</td>
                                <td>${merchant.legalName || '未填写'}</td>
                                <td>${merchant.contactPhone || '未填写'}</td>
                                <td>${formattedDate}</td>
                                <td>
                                    <button class="btn-view modal-view-btn" data-id="${merchant.id || merchant.username}">查看详情</button>
                                </td>
                            </tr>
                        `;
                    });
                    
                    return tableRows;
                };
                
                // 生成初始表格内容
                const initialTableRows = renderTable(currentPage);
                
                // 构建模态框内容
                // 不需要提示文字
                const rejectedNote = '';

                // 如果是已拒绝列表，更新统计卡片上的数量
                if (status === 'rejected') {
                    document.getElementById('rejectedMerchants').textContent = merchants.length;
                }

                modal.innerHTML = `
                    <div class="modal-content" style="max-width:900px; width:90%;">
                        <div class="modal-header" style="background-color:${style.bgColor}; color:${style.textColor}; display:flex; justify-content:space-between; align-items:center;">
                            <div style="display:flex; align-items:center;">
                                <h3 style="margin:0; margin-right:10px;">${title} (${merchants.length})</h3>
                                <button id="modalToggleSortBtn" class="btn-sort" title="切换排序" style="background-color:${style.bgColor}; border:1px solid ${style.textColor};">
                                    <i class="fas fa-sort-amount-down"></i>
                                    <span>最新优先</span>
                                </button>
                            </div>
                            <span class="close-modal">&times;</span>
                        </div>
                        <div class="modal-body" style="max-height:80vh; overflow-y:auto;">
                            ${rejectedNote}
                            
                            <!-- 添加搜索框 -->
                            <div class="search-box" style="margin-bottom: 15px; width: 100%; display: flex; justify-content: flex-end;">
                                <div style="position: relative; width: 300px;">
                                    <input type="text" id="modalSearchInput" placeholder="搜索用户名/公司/法人/电话..." style="width: 100%; padding: 8px 12px; padding-right: 30px; border: 1px solid #ddd; border-radius: 4px;">
                                    <i class="fas fa-search" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #777;"></i>
                                </div>
                            </div>
                            
                            <div class="table-container">
                                <table class="data-table" style="width:100%;">
                                    <thead>
                                        <tr>
                                            <th>申请用户</th>
                                            <th>公司名称</th>
                                            <th>法人名称</th>
                                            <th>联系电话</th>
                                            <th>${style.dateLabel}</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="modalTableBody">
                                        ${initialTableRows}
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- 分页控制 -->
                            <div class="table-footer" style="margin-top: 15px; display: flex; justify-content: space-between; align-items: center;">
                                <div class="pagination">
                                    <button class="page-btn" id="prevModalPage" ${currentPage <= 1 ? 'disabled' : ''}><i class="fas fa-angle-left"></i></button>
                                    <span class="page-info">第 <span id="currentModalPage">1</span> 页，共 <span id="totalModalPages">${totalPages}</span> 页</span>
                                    <div class="page-jump">
                                        <input type="number" id="jumpToModalPage" min="1" max="${totalPages}" placeholder="页码" title="输入页码后按回车跳转">
                                        <button class="jump-btn" id="jumpModalBtn" title="跳转到指定页码"><i class="fas fa-arrow-right"></i></button>
                                    </div>
                                    <button class="page-btn" id="nextModalPage" ${currentPage >= totalPages ? 'disabled' : ''}><i class="fas fa-angle-right"></i></button>
                                </div>
                                <div class="page-size">
                                    每页显示:
                                    <select id="modalPageSize">
                                        <option value="10" selected>10</option>
                                        <option value="20">20</option>
                                        <option value="50">50</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                // 添加到页面
                document.body.appendChild(modal);
                
                // 创建一个函数来处理模态框的关闭
                const closeThisModal = () => {
                    if (document.body.contains(modal)) {
                        document.body.removeChild(modal);
                    }
                };
                
                // 关闭按钮事件
                const closeBtn = modal.querySelector('.close-modal');
                if (closeBtn) {
                    closeBtn.addEventListener('click', function(e) {
                        e.stopPropagation(); // 阻止事件冒泡
                        closeThisModal();
                    }, { once: true }); // 使用 once 选项确保事件只触发一次
                }
                
                // 点击模态框外部关闭
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        closeThisModal();
                    }
                }, { once: true }); // 使用 once 选项确保事件只触发一次
                
                // 添加搜索功能
                const setupModalSearch = () => {
                    const searchInput = modal.querySelector('#modalSearchInput');
                    if (searchInput) {
                        // 使用防抖函数优化搜索性能
                        let searchTimeout;
                        searchInput.addEventListener('input', function() {
                            clearTimeout(searchTimeout);
                            searchTimeout = setTimeout(() => {
                                const searchTerm = this.value.trim().toLowerCase();
                                
                                if (searchTerm === '') {
                                    // 如果搜索框为空，恢复完整列表
                                    filteredMerchants = [...merchants];
                                } else {
                                    // 否则进行搜索过滤
                                    filteredMerchants = merchants.filter(merchant => {
                                        // 搜索用户名
                                        if (merchant.username && merchant.username.toLowerCase().includes(searchTerm)) {
                                            return true;
                                        }
                                        // 搜索公司名称
                                        if (merchant.companyName && merchant.companyName.toLowerCase().includes(searchTerm)) {
                                            return true;
                                        }
                                        // 搜索法人名称
                                        if (merchant.legalName && merchant.legalName.toLowerCase().includes(searchTerm)) {
                                            return true;
                                        }
                                        // 搜索联系电话
                                        if (merchant.contactPhone && merchant.contactPhone.includes(searchTerm)) {
                                            return true;
                                        }
                                        return false;
                                    });
                                }
                                
                                // 重新排序
                                sortMerchants();
                                
                                // 重新计算总页数
                                const totalPages = Math.ceil(filteredMerchants.length / pageSize);
                                modal.querySelector('#totalModalPages').textContent = totalPages;
                                
                                // 重置到第一页
                                modal.querySelector('#currentModalPage').textContent = '1';
                                
                                // 更新表格和分页按钮
                                const tableBody = modal.querySelector('#modalTableBody');
                                if (filteredMerchants.length === 0) {
                                    tableBody.innerHTML = `
                                        <tr>
                                            <td colspan="6" style="text-align:center; padding: 20px;">
                                                <i class="fas fa-search" style="margin-right: 10px;"></i>
                                                没有找到匹配"${searchTerm}"的商家
                                            </td>
                                        </tr>
                                    `;
                                } else {
                                    tableBody.innerHTML = renderTable(1);
                                }
                                
                                // 更新分页按钮状态
                                updatePaginationButtons();
                                
                                // 重新绑定查看详情按钮事件
                                setupViewDetailButtons();
                            }, 300); // 300毫秒防抖
                        });
                    }
                };
                
                // 排序功能
                const sortMerchants = () => {
                    // 根据排序方向对商家数据进行排序
                    filteredMerchants.sort((a, b) => {
                        // 决定使用哪个日期字段进行排序
                        let dateA, dateB;
                        
                        if (status === 'rejected') {
                            dateA = new Date(a.rejectedTime || a.lastRejectedTime || a.reviewTime || a.submitTime || 0).getTime();
                            dateB = new Date(b.rejectedTime || b.lastRejectedTime || b.reviewTime || b.submitTime || 0).getTime();
                        } else if (status === 'approved') {
                            dateA = new Date(a.reviewTime || a.submitTime || 0).getTime();
                            dateB = new Date(b.reviewTime || b.submitTime || 0).getTime();
                        } else {
                            dateA = new Date(a.submitTime || 0).getTime();
                            dateB = new Date(b.submitTime || 0).getTime();
                        }
                        
                        // 根据排序方向返回比较结果
                        return sortNewestFirst ? (dateB - dateA) : (dateA - dateB);
                    });
                };
                
                // 处理排序切换按钮
                const modalToggleSortBtn = modal.querySelector('#modalToggleSortBtn');
                if (modalToggleSortBtn) {
                    modalToggleSortBtn.addEventListener('click', function() {
                        // 切换排序方向
                        sortNewestFirst = !sortNewestFirst;
                        
                        // 更新按钮文本和图标
                        const iconElement = this.querySelector('i');
                        const textElement = this.querySelector('span');
                        
                        if (sortNewestFirst) {
                            iconElement.className = 'fas fa-sort-amount-down';
                            textElement.textContent = '最新优先';
                        } else {
                            iconElement.className = 'fas fa-sort-amount-up';
                            textElement.textContent = '最早优先';
                        }
                        
                        // 重新排序数据
                        sortMerchants();
                        
                        // 重置到第一页
                        modal.querySelector('#currentModalPage').textContent = '1';
                        
                        // 更新表格
                        const tableBody = modal.querySelector('#modalTableBody');
                        tableBody.innerHTML = renderTable(1);
                        
                        // 更新分页按钮状态
                        updatePaginationButtons();
                        
                        // 重新绑定查看详情按钮事件
                        setupViewDetailButtons();
                    });
                }
                
                // 设置分页功能
                const setupModalPagination = () => {
                    // 上一页按钮
                    const prevPageBtn = modal.querySelector('#prevModalPage');
                    if (prevPageBtn) {
                        prevPageBtn.addEventListener('click', function() {
                            if (!this.disabled) {
                                const currentPage = parseInt(modal.querySelector('#currentModalPage').textContent);
                                if (currentPage > 1) {
                                    // 更新页码
                                    modal.querySelector('#currentModalPage').textContent = currentPage - 1;
                                    // 重新渲染表格
                                    const tableBody = modal.querySelector('#modalTableBody');
                                    tableBody.innerHTML = renderTable(currentPage - 1);
                                    // 更新按钮状态
                                    updatePaginationButtons();
                                    // 重新绑定查看详情按钮事件
                                    setupViewDetailButtons();
                                }
                            }
                        });
                    }
                    
                    // 下一页按钮
                    const nextPageBtn = modal.querySelector('#nextModalPage');
                    if (nextPageBtn) {
                        nextPageBtn.addEventListener('click', function() {
                            if (!this.disabled) {
                                const currentPage = parseInt(modal.querySelector('#currentModalPage').textContent);
                                const totalPages = parseInt(modal.querySelector('#totalModalPages').textContent);
                                if (currentPage < totalPages) {
                                    // 更新页码
                                    modal.querySelector('#currentModalPage').textContent = currentPage + 1;
                                    // 重新渲染表格
                                    const tableBody = modal.querySelector('#modalTableBody');
                                    tableBody.innerHTML = renderTable(currentPage + 1);
                                    // 更新按钮状态
                                    updatePaginationButtons();
                                    // 重新绑定查看详情按钮事件
                                    setupViewDetailButtons();
                                }
                            }
                        });
                    }
                    
                    // 跳转到指定页
                    const jumpToPageInput = modal.querySelector('#jumpToModalPage');
                    const jumpBtn = modal.querySelector('#jumpModalBtn');
                    
                    // 处理跳转按钮点击
                    if (jumpBtn) {
                        jumpBtn.addEventListener('click', function() {
                            handleJumpToPage();
                        });
                    }
                    
                    // 处理输入框回车键
                    if (jumpToPageInput) {
                        jumpToPageInput.addEventListener('keypress', function(e) {
                            if (e.key === 'Enter') {
                                handleJumpToPage();
                            }
                        });
                    }
                    
                    // 页面跳转处理函数
                    const handleJumpToPage = () => {
                        const jumpToPage = parseInt(jumpToPageInput.value);
                        const totalPages = parseInt(modal.querySelector('#totalModalPages').textContent);
                        
                        if (jumpToPage && jumpToPage > 0 && jumpToPage <= totalPages) {
                            // 更新页码
                            modal.querySelector('#currentModalPage').textContent = jumpToPage;
                            // 重新渲染表格
                            const tableBody = modal.querySelector('#modalTableBody');
                            tableBody.innerHTML = renderTable(jumpToPage);
                            // 更新按钮状态
                            updatePaginationButtons();
                            // 重新绑定查看详情按钮事件
                            setupViewDetailButtons();
                            // 清空输入框
                            jumpToPageInput.value = '';
                        } else {
                            // 输入无效
                            showToast('请输入有效的页码', 'warning');
                            jumpToPageInput.focus();
                        }
                    };
                    
                    // 每页显示数量变化
                    const pageSizeSelect = modal.querySelector('#modalPageSize');
                    if (pageSizeSelect) {
                        pageSizeSelect.addEventListener('change', function() {
                            const newPageSize = parseInt(this.value);
                            // 更新pageSize
                            pageSize = newPageSize;
                            // 计算新的总页数
                            const totalPages = Math.ceil(filteredMerchants.length / newPageSize);
                            modal.querySelector('#totalModalPages').textContent = totalPages;
                            
                            // 确保当前页在有效范围内
                            let currentPage = parseInt(modal.querySelector('#currentModalPage').textContent);
                            if (currentPage > totalPages) {
                                currentPage = totalPages;
                                modal.querySelector('#currentModalPage').textContent = currentPage;
                            }
                            
                            // 重新渲染表格
                            const tableBody = modal.querySelector('#modalTableBody');
                            tableBody.innerHTML = renderTable(currentPage);
                            
                            // 更新按钮状态
                            updatePaginationButtons();
                            
                            // 重新绑定查看详情按钮事件
                            setupViewDetailButtons();
                        });
                    }
                    
                    // 更新分页按钮状态
                    const updatePaginationButtons = () => {
                        const currentPage = parseInt(modal.querySelector('#currentModalPage').textContent);
                        const totalPages = parseInt(modal.querySelector('#totalModalPages').textContent);
                        
                        modal.querySelector('#prevModalPage').disabled = currentPage <= 1;
                        modal.querySelector('#nextModalPage').disabled = currentPage >= totalPages;
                    };
                };
                
                // 设置查看详情按钮事件
                const setupViewDetailButtons = () => {
                    // 查看详情按钮事件
                    modal.querySelectorAll('.modal-view-btn').forEach(btn => {
                        btn.addEventListener('click', function(e) {
                            e.stopPropagation(); // 阻止事件冒泡
                            const merchantId = this.getAttribute('data-id');
                            // 关闭当前模态框
                            closeThisModal();
                            
                            // 如果是从已拒绝商家列表打开，不显示审核按钮
                            const fromRejectedList = status === 'rejected';
                            // 打开详情模态框，只有非已拒绝列表的待审核商家才显示审核按钮
                            viewMerchantDetails(merchantId, !fromRejectedList);
                        });
                    });
                };
                
                // 初始排序
                sortMerchants();
                
                // 初始化分页功能
                setupModalPagination();
                
                // 初始化搜索功能
                setupModalSearch();
                
                // 初始化查看详情按钮
                setupViewDetailButtons();
            };
            
            // 获取状态文本（已移除）
            const getStatusText = (status) => {
                // 该功能已被移除，但为了保持代码兼容性而保留简化版本
                switch(status) {
                    case 'pending': return '待审核';
                    case 'approved': return '已认证';
                    case 'rejected': return '已拒绝';
                    default: return '未知状态';
                }
            };
            
            // 商品数据存储
            let productsData = [
                { id: 'P001', name: '商品A', price: '¥199.00', stock: 100, status: '上架中', description: '这是商品A的描述' },
                { id: 'P002', name: '商品B', price: '¥299.00', stock: 50, status: '上架中', description: '这是商品B的描述' },
                { id: 'P003', name: '商品C', price: '¥99.00', stock: 0, status: '缺货', description: '这是商品C的描述' }
            ];
            
            // 加载商品管理数据
            const loadProductsData = () => {
                const productsList = document.getElementById('productsList');
                
                if (productsList) {
                    // 显示加载状态
                    productsList.innerHTML = '<tr><td colspan="6" class="table-empty">正在加载商品数据...</td></tr>';
                    
                    // 获取搜索关键词
                    const searchTerm = (document.getElementById('productSearchInput')?.value || '').trim().toLowerCase();
                    
                    // 应用搜索过滤
                    let filteredProducts = productsData;
                    if (searchTerm) {
                        filteredProducts = productsData.filter(product => {
                            return product.id.toLowerCase().includes(searchTerm) ||
                                product.name.toLowerCase().includes(searchTerm) ||
                                product.status.toLowerCase().includes(searchTerm);
                        });
                    }
                    
                    setTimeout(() => {
                        // 模拟数据加载延迟
                        if (filteredProducts.length === 0) {
                            productsList.innerHTML = searchTerm ? 
                                `<tr><td colspan="6" class="table-empty">没有找到匹配 "${searchTerm}" 的商品</td></tr>` :
                                '<tr><td colspan="6" class="table-empty">暂无商品数据</td></tr>';
                                
                            // 更新分页信息
                            document.getElementById('currentProductPage').textContent = '1';
                            document.getElementById('totalProductPages').textContent = '1';
                            document.getElementById('prevProductPage').disabled = true;
                            document.getElementById('nextProductPage').disabled = true;
                        } else {
                            // 分页处理
                            const pageSize = parseInt(document.getElementById('productPageSize').value) || 10;
                            const totalPages = Math.ceil(filteredProducts.length / pageSize);
                            let currentPage = parseInt(document.getElementById('currentProductPage').textContent) || 1;
                            
                            // 确保当前页在有效范围内
                            if (currentPage > totalPages) {
                                currentPage = totalPages;
                            }
                            if (currentPage < 1) {
                                currentPage = 1;
                            }
                            
                            // 更新页码信息
                            document.getElementById('currentProductPage').textContent = currentPage;
                            document.getElementById('totalProductPages').textContent = totalPages;
                            
                            // 启用或禁用上一页、下一页按钮
                            document.getElementById('prevProductPage').disabled = (currentPage <= 1);
                            document.getElementById('nextProductPage').disabled = (currentPage >= totalPages);
                            
                            // 计算当前页的起始和结束索引
                            const startIndex = (currentPage - 1) * pageSize;
                            const endIndex = Math.min(startIndex + pageSize, filteredProducts.length);
                            
                            // 清空现有内容
                            productsList.innerHTML = '';
                            
                            // 只显示当前页的商品
                            for (let i = startIndex; i < endIndex; i++) {
                                const product = filteredProducts[i];
                                const row = document.createElement('tr');
                                
                                // 设置状态样式
                                let statusClass = '';
                                if (product.status === '上架中') {
                                    statusClass = 'status-active';
                                } else if (product.status === '缺货') {
                                    statusClass = 'status-pending';
                                } else if (product.status === '下架') {
                                    statusClass = 'status-inactive';
                                }
                                
                                row.innerHTML = `
                                    <td>${product.id}</td>
                                    <td>${product.name}</td>
                                    <td>${product.price}</td>
                                    <td>${product.stock}</td>
                                    <td><span class="${statusClass}">${product.status}</span></td>
                                    <td>
                                        <button class="btn-view" data-id="${product.id}">查看</button>
                                        <button class="btn-edit" data-id="${product.id}">编辑</button>
                                        <button class="btn-delete" data-id="${product.id}">删除</button>
                                    </td>
                                `;
                                
                                productsList.appendChild(row);
                            }
                            
                            // 添加商品操作按钮事件
                            setupProductActionButtons();
                        }
                    }, 300);
                }
                
                // 设置添加商品按钮事件
                const addProductBtn = document.getElementById('addProduct');
                if (addProductBtn) {
                    // 移除现有事件监听器，避免重复添加
                    const newAddProductBtn = addProductBtn.cloneNode(true);
                    addProductBtn.parentNode.replaceChild(newAddProductBtn, addProductBtn);
                    
                    // 添加新的事件监听器
                    newAddProductBtn.addEventListener('click', function() {
                        // 显示添加商品模态框
                        document.getElementById('addProductModal').style.display = 'flex';
                    });
                }
            };
            
            // 设置商品操作按钮事件
            const setupProductActionButtons = () => {
                // 查看商品按钮
                document.querySelectorAll('#productsList .btn-view').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const productId = this.getAttribute('data-id');
                        const product = productsData.find(p => p.id === productId);
                        if (product) {
                            viewProductDetails(product);
                        }
                    });
                });
                
                // 编辑商品按钮
                document.querySelectorAll('#productsList .btn-edit').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const productId = this.getAttribute('data-id');
                        // 跳转到编辑页面而不是打开模态框
                        window.location.href = `publish-product.html?edit=${productId}`;
                    });
                });
                
                // 删除商品按钮
                document.querySelectorAll('#productsList .btn-delete').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const productId = this.getAttribute('data-id');
                        if (confirm(`确定要删除商品 ${productId} 吗？`)) {
                            // 从数组中移除商品
                            productsData = productsData.filter(p => p.id !== productId);
                            // 重新加载列表
                            loadProductsData();
                            showToast(`商品 ${productId} 已删除`, 'info', '删除成功');
                        }
                    });
                });
            };
            
            // 渲染商品价格（用于模态框）
            function renderProductPriceForModal(product) {
                if (product.types && Array.isArray(product.types) && product.types.length > 0) {
                    // 新格式：商品类型
                    if (product.types.length === 1) {
                        return `¥${parseFloat(product.types[0].price).toFixed(2)} (${product.types[0].name})`;
                    } else {
                        const prices = product.types.map(type => parseFloat(type.price));
                        const minPrice = Math.min(...prices);
                        const maxPrice = Math.max(...prices);
                        const typesList = product.types.map(type => `${type.name}: ¥${parseFloat(type.price).toFixed(2)}`).join(', ');
                        return `¥${minPrice.toFixed(2)} - ¥${maxPrice.toFixed(2)} (${typesList})`;
                    }
                } else if (product.price) {
                    // 旧格式：单一价格
                    return `¥${parseFloat(product.price).toFixed(2)}`;
                } else {
                    return '价格待定';
                }
            }

            // 查看商品详情函数
            function viewProductDetails(product) {
                // 创建模态框
                const modal = document.createElement('div');
                modal.className = 'modal';
                modal.style.display = 'flex';
                
                modal.innerHTML = `
                    <div class="modal-content" style="max-width: 500px; width: 90%;">
                        <div class="modal-header">
                            <h3>商品详情</h3>
                            <span class="close-modal">&times;</span>
                        </div>
                        <div class="modal-body">
                            <div style="margin-bottom: 15px;">
                                <strong>商品ID:</strong> ${product.id}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>商品名称:</strong> ${product.name}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>价格:</strong> ${renderProductPriceForModal(product)}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>库存:</strong> ${product.stock}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>状态:</strong> 
                                <span class="${product.status === '上架中' ? 'status-active' : product.status === '缺货' ? 'status-pending' : 'status-inactive'}">
                                    ${product.status}
                                </span>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>商品描述:</strong> 
                                <p style="margin-top: 5px;">${product.description || '无描述'}</p>
                            </div>
                            <div style="text-align: right; margin-top: 20px;">
                                <button type="button" class="btn-close" style="background-color: #f5f5f5; color: #333; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">关闭</button>
                            </div>
                        </div>
                    </div>
                `;
                
                // 添加到页面
                document.body.appendChild(modal);
                
                // 添加关闭事件
                const closeModalBtn = modal.querySelector('.close-modal');
                const closeBtn = modal.querySelector('.btn-close');
                
                closeModalBtn.addEventListener('click', () => {
                    document.body.removeChild(modal);
                });
                
                closeBtn.addEventListener('click', () => {
                    document.body.removeChild(modal);
                });
                
                // 点击模态框外部关闭
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        document.body.removeChild(modal);
                    }
                });
            }
            
            // 编辑商品函数
            function editProduct(product) {
                // 创建编辑模态框
                const modal = document.createElement('div');
                modal.className = 'modal';
                modal.style.display = 'flex';
                
                modal.innerHTML = `
                    <div class="modal-content" style="max-width: 500px; width: 90%;">
                        <div class="modal-header">
                            <h3>编辑商品</h3>
                            <span class="close-modal">&times;</span>
                        </div>
                        <div class="modal-body">
                            <form id="editProductForm">
                                <div style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">商品ID</label>
                                    <input type="text" value="${product.id}" disabled style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background-color: #f5f5f5;">
                                </div>
                                
                                <div style="margin-bottom: 15px;">
                                    <label for="editProductName" style="display: block; margin-bottom: 5px; font-weight: bold;">商品名称</label>
                                    <input type="text" id="editProductName" value="${product.name}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                                </div>
                                
                                <div style="margin-bottom: 15px;">
                                    <label for="editProductPrice" style="display: block; margin-bottom: 5px; font-weight: bold;">价格</label>
                                    <input type="text" id="editProductPrice" value="${product.price.replace('¥', '')}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                                </div>
                                
                                <div style="margin-bottom: 15px;">
                                    <label for="editProductStock" style="display: block; margin-bottom: 5px; font-weight: bold;">库存</label>
                                    <input type="number" id="editProductStock" value="${product.stock}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                                </div>
                                
                                <div style="margin-bottom: 15px;">
                                    <label for="editProductStatus" style="display: block; margin-bottom: 5px; font-weight: bold;">状态</label>
                                    <select id="editProductStatus" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                                        <option value="上架中" ${product.status === '上架中' ? 'selected' : ''}>上架中</option>
                                        <option value="缺货" ${product.status === '缺货' ? 'selected' : ''}>缺货</option>
                                        <option value="下架" ${product.status === '下架' ? 'selected' : ''}>下架</option>
                                    </select>
                                </div>
                                
                                <div style="margin-bottom: 15px;">
                                    <label for="editProductDesc" style="display: block; margin-bottom: 5px; font-weight: bold;">商品描述</label>
                                    <textarea id="editProductDesc" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; height: 100px;">${product.description || ''}</textarea>
                                </div>
                                
                                <div style="text-align: right; margin-top: 20px;">
                                    <button type="button" class="btn-cancel" style="background-color: #f5f5f5; color: #333; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-right: 10px;">取消</button>
                                    <button type="submit" class="btn-submit" style="background-color: #28a745; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">保存</button>
                                </div>
                            </form>
                        </div>
                    </div>
                `;
                
                // 添加到页面
                document.body.appendChild(modal);
                
                // 添加关闭事件
                const closeModalBtn = modal.querySelector('.close-modal');
                const cancelBtn = modal.querySelector('.btn-cancel');
                
                closeModalBtn.addEventListener('click', () => {
                    document.body.removeChild(modal);
                });
                
                cancelBtn.addEventListener('click', () => {
                    document.body.removeChild(modal);
                });
                
                // 点击模态框外部关闭
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        document.body.removeChild(modal);
                    }
                });
                
                // 表单提交事件
                const editForm = modal.querySelector('#editProductForm');
                if (editForm) {
                    editForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        
                        // 获取修改后的数据
                        const name = document.getElementById('editProductName').value;
                        const price = '¥' + document.getElementById('editProductPrice').value;
                        const stock = parseInt(document.getElementById('editProductStock').value);
                        const status = document.getElementById('editProductStatus').value;
                        const description = document.getElementById('editProductDesc').value;
                        
                        // 更新商品数据
                        const index = productsData.findIndex(p => p.id === product.id);
                        if (index !== -1) {
                            productsData[index] = {
                                ...productsData[index],
                                name,
                                price,
                                stock,
                                status,
                                description
                            };
                        }
                        
                        // 关闭模态框
                        document.body.removeChild(modal);
                        
                        // 显示成功提示
                        showToast(`商品 ${name} 信息已更新`, 'success', '更新成功');
                        
                        // 重新加载商品列表
                        loadProductsData();
                    });
                }
            }
            
            // 设置添加商品表单的事件
            document.addEventListener('DOMContentLoaded', function() {
                // 设置添加商品模态框的关闭按钮事件
                const productModalClose = document.querySelector('#addProductModal .close-modal');
                if (productModalClose) {
                    productModalClose.addEventListener('click', function() {
                        document.getElementById('addProductModal').style.display = 'none';
                    });
                }
                
                // 设置添加商品表单的取消按钮事件
                const productCancelBtn = document.querySelector('#addProductModal .btn-cancel');
                if (productCancelBtn) {
                    productCancelBtn.addEventListener('click', function() {
                        document.getElementById('addProductModal').style.display = 'none';
                    });
                }
                
                // 设置添加商品表单的提交事件
                const productForm = document.getElementById('addProductForm');
                if (productForm) {
                    productForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        
                        // 获取表单数据
                        const name = document.getElementById('productName').value;
                        const priceValue = document.getElementById('productPrice').value;
                        const price = priceValue.startsWith('¥') ? priceValue : '¥' + priceValue;
                        const stock = parseInt(document.getElementById('productStock').value);
                        const status = document.getElementById('productStatus').value;
                        const description = document.getElementById('productDesc').value;
                        
                        // 生成唯一ID
                        const id = 'P' + (Math.floor(Math.random() * 900) + 100);
                        
                        // 创建新商品对象
                        const newProduct = {
                            id,
                            name,
                            price,
                            stock,
                            status,
                            description
                        };
                        
                        // 添加到商品数据中
                        productsData.unshift(newProduct);
                        
                        // 关闭模态框
                        document.getElementById('addProductModal').style.display = 'none';
                        
                        // 重置表单
                        productForm.reset();
                        
                        // 显示成功提示
                        showToast(`成功添加商品 ${name}`, 'success', '添加成功');
                        
                        // 重新加载商品列表
                        loadProductsData();
                    });
                }
            });
            
            // 设置商品分页控制
            const setupProductPagination = () => {
                // 上一页按钮
                const prevPageBtn = document.getElementById('prevProductPage');
                if (prevPageBtn) {
                    prevPageBtn.addEventListener('click', function() {
                        if (!this.disabled) {
                            const currentPage = parseInt(document.getElementById('currentProductPage').textContent);
                            if (currentPage > 1) {
                                document.getElementById('currentProductPage').textContent = currentPage - 1;
                                loadProductsData();
                            }
                        }
                    });
                }
                
                // 下一页按钮
                const nextPageBtn = document.getElementById('nextProductPage');
                if (nextPageBtn) {
                    nextPageBtn.addEventListener('click', function() {
                        if (!this.disabled) {
                            const currentPage = parseInt(document.getElementById('currentProductPage').textContent);
                            const totalPages = parseInt(document.getElementById('totalProductPages').textContent);
                            if (currentPage < totalPages) {
                                document.getElementById('currentProductPage').textContent = currentPage + 1;
                                loadProductsData();
                            }
                        }
                    });
                }
                
                // 处理页面跳转
                const jumpToPageInput = document.getElementById('jumpToProductPage');
                const jumpBtn = document.getElementById('jumpProductBtn');
                
                // 跳转按钮点击
                if (jumpBtn) {
                    jumpBtn.addEventListener('click', function() {
                        handleJumpToProductPage();
                    });
                }
                
                // 跳转输入框回车
                if (jumpToPageInput) {
                    jumpToPageInput.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            handleJumpToProductPage();
                        }
                    });
                }
                
                // 处理页面跳转
                const handleJumpToProductPage = () => {
                    const jumpToPage = parseInt(jumpToPageInput.value);
                    const totalPages = parseInt(document.getElementById('totalProductPages').textContent);
                    
                    if (jumpToPage && jumpToPage > 0 && jumpToPage <= totalPages) {
                        document.getElementById('currentProductPage').textContent = jumpToPage;
                        loadProductsData();
                        // 清空输入框
                        jumpToPageInput.value = '';
                    } else {
                        // 输入无效
                        showToast('请输入有效的页码', 'warning');
                        jumpToPageInput.focus();
                    }
                };
                
                // 处理搜索输入
                const productSearchInput = document.getElementById('productSearchInput');
                if (productSearchInput) {
                    // 使用防抖函数优化搜索性能
                    let searchTimeout;
                    productSearchInput.addEventListener('input', function() {
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(() => {
                            // 重置到第一页
                            document.getElementById('currentProductPage').textContent = '1';
                            // 重新加载带有搜索条件的数据
                            loadProductsData();
                        }, 300); // 300毫秒防抖
                    });
                }
            };

            // 设置已发布商品分页控制
            const setupPublishedProductsPagination = () => {
                // 上一页按钮
                const prevPageBtn = document.getElementById('prevPublishedPage');
                if (prevPageBtn) {
                    prevPageBtn.addEventListener('click', function() {
                        if (!this.disabled) {
                            const currentPage = parseInt(document.getElementById('currentPublishedPage').textContent);
                            if (currentPage > 1) {
                                document.getElementById('currentPublishedPage').textContent = currentPage - 1;
                                renderPublishedProducts(allPublishedProducts);
                            }
                        }
                    });
                }

                // 下一页按钮
                const nextPageBtn = document.getElementById('nextPublishedPage');
                if (nextPageBtn) {
                    nextPageBtn.addEventListener('click', function() {
                        if (!this.disabled) {
                            const currentPage = parseInt(document.getElementById('currentPublishedPage').textContent);
                            const totalPages = parseInt(document.getElementById('totalPublishedPages').textContent);
                            if (currentPage < totalPages) {
                                document.getElementById('currentPublishedPage').textContent = currentPage + 1;
                                renderPublishedProducts(allPublishedProducts);
                            }
                        }
                    });
                }

                // 处理页面跳转
                const jumpToPageInput = document.getElementById('jumpToPublishedPage');
                const jumpBtn = document.getElementById('jumpPublishedBtn');

                // 跳转按钮点击
                if (jumpBtn) {
                    jumpBtn.addEventListener('click', function() {
                        handleJumpToPublishedPage();
                    });
                }

                // 跳转输入框回车
                if (jumpToPageInput) {
                    jumpToPageInput.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            handleJumpToPublishedPage();
                        }
                    });
                }

                // 处理页面跳转
                const handleJumpToPublishedPage = () => {
                    const jumpToPage = parseInt(jumpToPageInput.value);
                    const totalPages = parseInt(document.getElementById('totalPublishedPages').textContent);

                    if (jumpToPage && jumpToPage > 0 && jumpToPage <= totalPages) {
                        document.getElementById('currentPublishedPage').textContent = jumpToPage;
                        renderPublishedProducts(allPublishedProducts);
                        // 清空输入框
                        jumpToPageInput.value = '';
                    } else {
                        // 输入无效
                        showToast('请输入有效的页码', 'warning');
                        jumpToPageInput.focus();
                    }
                };

                // 每页显示数量变化
                const pageSizeSelect = document.getElementById('publishedPageSize');
                if (pageSizeSelect) {
                    pageSizeSelect.addEventListener('change', function() {
                        // 重置到第一页
                        document.getElementById('currentPublishedPage').textContent = '1';
                        // 重新渲染
                        renderPublishedProducts(allPublishedProducts);
                    });
                }

                // 搜索功能
                const searchInput = document.getElementById('publishedProductsSearchInput');
                if (searchInput) {
                    // 使用防抖函数优化搜索性能
                    let searchTimeout;
                    searchInput.addEventListener('input', function() {
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(() => {
                            // 重置到第一页
                            document.getElementById('currentPublishedPage').textContent = '1';
                            // 重新渲染带有搜索条件的数据
                            renderPublishedProducts(allPublishedProducts);
                        }, 300); // 300毫秒防抖
                    });
                }
            };

            // 加载客服管理数据
            const loadCustomerServiceData = () => {
                // 加载客服人员数据
                loadCustomerServiceStaff();
                
                // 设置分页控制
                setupCSPagination();
            };
            
            // 设置客服分页控制
            const setupCSPagination = () => {
                // 处理搜索输入
                const csSearchInput = document.getElementById('csSearchInput');
                if (csSearchInput) {
                    // 使用防抖函数优化搜索性能
                    let searchTimeout;
                    csSearchInput.addEventListener('input', function() {
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(() => {
                            // 重置到第一页
                            document.getElementById('currentCSPage').textContent = '1';
                            // 重新加载带有搜索条件的数据
                            loadCustomerServiceStaff();
                        }, 300); // 300毫秒防抖
                    });
                }
                
                // 上一页按钮
                const prevPageBtn = document.getElementById('prevCSPage');
                if (prevPageBtn) {
                    prevPageBtn.addEventListener('click', function() {
                        if (!this.disabled) {
                            const currentPage = parseInt(document.getElementById('currentCSPage').textContent);
                            if (currentPage > 1) {
                                document.getElementById('currentCSPage').textContent = currentPage - 1;
                                loadCustomerServiceStaff();
                            }
                        }
                    });
                }
                
                // 下一页按钮
                const nextPageBtn = document.getElementById('nextCSPage');
                if (nextPageBtn) {
                    nextPageBtn.addEventListener('click', function() {
                        if (!this.disabled) {
                            const currentPage = parseInt(document.getElementById('currentCSPage').textContent);
                            const totalPages = parseInt(document.getElementById('totalCSPages').textContent);
                            if (currentPage < totalPages) {
                                document.getElementById('currentCSPage').textContent = currentPage + 1;
                                loadCustomerServiceStaff();
                            }
                        }
                    });
                }
                
                // 每页显示数量变化
                const pageSizeSelect = document.getElementById('csPageSize');
                if (pageSizeSelect) {
                    pageSizeSelect.addEventListener('change', function() {
                        // 重置到第一页
                        document.getElementById('currentCSPage').textContent = '1';
                        // 重新加载数据
                        loadCustomerServiceStaff();
                    });
                }
                
                // 处理页面跳转
                const jumpToPageInput = document.getElementById('jumpToCSPage');
                const jumpBtn = document.getElementById('jumpCSBtn');
                
                // 跳转按钮点击
                if (jumpBtn) {
                    jumpBtn.addEventListener('click', function() {
                        handleJumpToCSPage();
                    });
                }
                
                // 跳转输入框回车
                if (jumpToPageInput) {
                    jumpToPageInput.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            handleJumpToCSPage();
                        }
                    });
                }
                
                // 处理页面跳转
                const handleJumpToCSPage = () => {
                    const jumpToPage = parseInt(jumpToPageInput.value);
                    const totalPages = parseInt(document.getElementById('totalCSPages').textContent);
                    
                    if (jumpToPage && jumpToPage > 0 && jumpToPage <= totalPages) {
                        document.getElementById('currentCSPage').textContent = jumpToPage;
                        loadCustomerServiceStaff();
                        // 清空输入框
                        jumpToPageInput.value = '';
                    } else {
                        // 输入无效
                        showToast('请输入有效的页码', 'warning');
                        jumpToPageInput.focus();
                    }
                };
            };
            
            // 客服人员数据存储
let customerServiceStaff = [];

// 加载客服人员数据
const loadCustomerServiceStaff = () => {
    const csStaffList = document.getElementById('csStaffList');
    
    if (csStaffList) {
        // 清空现有内容
        csStaffList.innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 20px;">正在加载客服数据...</td></tr>';
        
        // 从服务器获取客服数据
        fetch('/api/customer-service')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    customerServiceStaff = data.customerServiceStaff || [];
                    
                    // 获取搜索关键词
                    const searchTerm = (document.getElementById('csSearchInput')?.value || '').trim().toLowerCase();
                    
                    // 如果有搜索关键词，筛选结果
                    let filteredStaff = customerServiceStaff;
                    if (searchTerm) {
                        filteredStaff = customerServiceStaff.filter(staff => {
                            // 在ID中搜索
                            if (staff.id && staff.id.toString().toLowerCase().includes(searchTerm)) {
                                return true;
                            }
                            
                            // 在姓名中搜索
                            if (staff.name && staff.name.toLowerCase().includes(searchTerm)) {
                                return true;
                            }
                            
                            // 在电话中搜索
                            if (staff.phone && staff.phone.includes(searchTerm)) {
                                return true;
                            }
                            
                            // 在邮箱中搜索
                            if (staff.email && staff.email.toLowerCase().includes(searchTerm)) {
                                return true;
                            }
                            
                            // 在微信中搜索
                            if (staff.wechat && staff.wechat.toLowerCase().includes(searchTerm)) {
                                return true;
                            }
                            
                            return false;
                        });
                        
                        // 如果没有匹配的结果
                        if (filteredStaff.length === 0) {
                            csStaffList.innerHTML = `<tr><td colspan="5" style="text-align: center; padding: 20px;">没有找到匹配 "${searchTerm}" 的客服</td></tr>`;
                            document.getElementById('currentCSPage').textContent = '1';
                            document.getElementById('totalCSPages').textContent = '1';
                            document.getElementById('prevCSPage').disabled = true;
                            document.getElementById('nextCSPage').disabled = true;
                            return;
                        }
                    }
                    
                    // 如果没有客服数据
                    if (filteredStaff.length === 0) {
                        // 显示暂无数据的提示
                        csStaffList.innerHTML = `<tr><td colspan="5" style="text-align: center; padding: 20px;">暂无客服数据</td></tr>`;
                        document.getElementById('currentCSPage').textContent = '1';
                        document.getElementById('totalCSPages').textContent = '1';
                        document.getElementById('prevCSPage').disabled = true;
                        document.getElementById('nextCSPage').disabled = true;
                        return;
                    }
                    
                    // 分页处理
                    const pageSize = parseInt(document.getElementById('csPageSize').value) || 10;
                    const totalPages = Math.ceil(filteredStaff.length / pageSize);
                    let currentPage = parseInt(document.getElementById('currentCSPage').textContent) || 1;
                    
                    // 确保当前页在有效范围内
                    if (currentPage > totalPages) {
                        currentPage = totalPages;
                    }
                    if (currentPage < 1) {
                        currentPage = 1;
                    }
                    
                    // 更新页码信息
                    document.getElementById('currentCSPage').textContent = currentPage;
                    document.getElementById('totalCSPages').textContent = totalPages;
                    
                    // 启用或禁用上一页、下一页按钮
                    document.getElementById('prevCSPage').disabled = (currentPage <= 1);
                    document.getElementById('nextCSPage').disabled = (currentPage >= totalPages);
                    
                    // 计算当前页的起始和结束索引
                    const startIndex = (currentPage - 1) * pageSize;
                    const endIndex = Math.min(startIndex + pageSize, filteredStaff.length);
                    
                    // 清空现有内容
                    csStaffList.innerHTML = '';
                    
                    // 只显示当前页的客服
                    for (let i = startIndex; i < endIndex; i++) {
                        const staff = filteredStaff[i];
                        const row = document.createElement('tr');
                        
                        // 设置状态文本和类名
                        let statusText = '';
                        let statusClass = '';
                        
                        if (staff.status === 'active') {
                            statusText = '在线';
                            statusClass = 'status-active';
                        } else if (staff.status === 'busy') {
                            statusText = '忙碌';
                            statusClass = 'status-busy';
                        } else if (staff.status === 'inactive') {
                            statusText = '离线';
                            statusClass = 'status-inactive';
                        }
                        
                        row.innerHTML = `
                            <td>${staff.id}</td>
                            <td>${staff.name}</td>
                            <td>${staff.phone}</td>
                            <td><span class="${statusClass}">${statusText}</span></td>
                            <td>
                                <button class="btn-view" data-id="${staff.id}">查看</button>
                                <button class="btn-edit" data-id="${staff.id}">编辑</button>
                                <button class="btn-delete" data-id="${staff.id}">删除</button>
                            </td>
                        `;
                        
                        csStaffList.appendChild(row);
                    }
                    
                    // 为查看按钮添加事件
                    document.querySelectorAll('#csStaffList .btn-view').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const staffId = this.getAttribute('data-id');
                            const staff = customerServiceStaff.find(s => s.id === staffId);
                            if (staff) {
                                viewCustomerServiceDetails(staff);
                            }
                        });
                    });
                    
                    // 为编辑按钮添加事件
                    document.querySelectorAll('#csStaffList .btn-edit').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const staffId = this.getAttribute('data-id');
                            const staff = customerServiceStaff.find(s => s.id === staffId);
                            if (staff) {
                                editCustomerService(staff);
                            }
                        });
                    });
                    
                    // 为删除按钮添加事件
                    document.querySelectorAll('#csStaffList .btn-delete').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const staffId = this.getAttribute('data-id');
                            if (confirm(`确定要删除客服 ${staffId} 吗？`)) {
                                // 调用服务器API删除客服
                                fetch(`/api/customer-service/${staffId}`, {
                                    method: 'DELETE'
                                })
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success) {
                                        // 从本地数组中移除
                                        customerServiceStaff = customerServiceStaff.filter(s => s.id !== staffId);
                                        // 重新加载列表
                                        loadCustomerServiceStaff();
                                        showToast(`客服 ${staffId} 已删除`, 'info', '删除成功');
                                    } else {
                                        showToast(data.message, 'error', '删除失败');
                                    }
                                })
                                .catch(error => {
                                    console.error('删除客服失败:', error);
                                    showToast('网络错误，请稍后再试', 'error', '删除失败');
                                });
                            }
                        });
                    });
                } else {
                    showToast(data.message || '获取客服数据失败', 'error', '加载失败');
                }
            })
            .catch(error => {
                console.error('获取客服数据失败:', error);
                csStaffList.innerHTML = `<tr><td colspan="5" style="text-align: center; padding: 20px;">加载失败，请稍后再试</td></tr>`;
            });
    }
};
            

            
            // 添加客服按钮事件
            const addCustomerServiceBtn = document.getElementById('addCustomerService');
            if (addCustomerServiceBtn) {
                addCustomerServiceBtn.addEventListener('click', function() {
                    // 显示添加客服模态框
                    document.getElementById('addCustomerServiceModal').style.display = 'flex';
                });
            }
            
            // 设置添加客服模态框的关闭按钮事件
            const csModalClose = document.querySelector('#addCustomerServiceModal .close-modal');
            if (csModalClose) {
                csModalClose.addEventListener('click', function() {
                    document.getElementById('addCustomerServiceModal').style.display = 'none';
                });
            }
            
            // 设置添加客服表单的取消按钮事件
            const csCancelBtn = document.querySelector('#addCustomerServiceModal .btn-cancel');
            if (csCancelBtn) {
                csCancelBtn.addEventListener('click', function() {
                    document.getElementById('addCustomerServiceModal').style.display = 'none';
                });
            }
            
            // 设置添加客服表单的提交事件
const csForm = document.getElementById('addCustomerServiceForm');
if (csForm) {
    csForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // 获取表单数据
        const account = document.getElementById('csAccount').value;
        const password = document.getElementById('csPassword').value;
        const name = document.getElementById('csName').value;
        const email = document.getElementById('csEmail').value;
        const phone = document.getElementById('csPhone').value;
        const wechat = document.getElementById('csWechat').value;
        
        // 创建请求数据
        const newStaffData = {
            account: account,
            password: password,
            name: name,
            email: email,
            phone: phone,
            wechat: wechat
        };
        
        // 发送到服务器
        fetch('/api/customer-service', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(newStaffData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 添加成功
                // 显示成功提示
                showToast(`成功添加客服 ${name}`, 'success', '添加成功');
                
                // 关闭模态框
                document.getElementById('addCustomerServiceModal').style.display = 'none';
                
                // 重置表单
                csForm.reset();
                
                // 重新加载客服列表
                loadCustomerServiceStaff();
            } else {
                // 添加失败
                showToast(data.message || '添加客服失败', 'error', '添加失败');
            }
        })
        .catch(error => {
            console.error('添加客服失败:', error);
            showToast('网络错误，请稍后再试', 'error', '添加失败');
        });
    });
}
            
            // 查看客服详情函数
            function viewCustomerServiceDetails(staff) {
                // 创建模态框
                const modal = document.createElement('div');
                modal.className = 'modal';
                modal.style.display = 'flex';
                
                modal.innerHTML = `
                    <div class="modal-content" style="max-width: 500px; width: 90%;">
                        <div class="modal-header">
                            <h3>客服详情</h3>
                            <span class="close-modal">&times;</span>
                        </div>
                        <div class="modal-body">
                            <div style="margin-bottom: 15px;">
                                <strong>客服ID:</strong> ${staff.id}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>账号:</strong> ${staff.account}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>姓名:</strong> ${staff.name}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>邮箱:</strong> ${staff.email || '未设置'}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>电话:</strong> ${staff.phone}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>微信:</strong> ${staff.wechat || '未设置'}
                            </div>
                            
                            <div style="margin-bottom: 15px;">
                                <strong>状态:</strong> 
                                <span class="${staff.status === 'active' ? 'status-active' : staff.status === 'busy' ? 'status-busy' : 'status-inactive'}">
                                    ${staff.status === 'active' ? '在线' : staff.status === 'busy' ? '忙碌' : '离线'}
                                </span>
                            </div>
                            <div style="text-align: right; margin-top: 20px;">
                                <button type="button" class="btn-close" style="background-color: #f5f5f5; color: #333; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">关闭</button>
                            </div>
                        </div>
                    </div>
                `;
                
                // 添加到页面
                document.body.appendChild(modal);
                
                // 添加关闭事件
                const closeModalBtn = modal.querySelector('.close-modal');
                const closeBtn = modal.querySelector('.btn-close');
                
                closeModalBtn.addEventListener('click', () => {
                    document.body.removeChild(modal);
                });
                
                closeBtn.addEventListener('click', () => {
                    document.body.removeChild(modal);
                });
                
                // 点击模态框外部关闭
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        document.body.removeChild(modal);
                    }
                });
            }
            
            // 编辑客服函数
            function editCustomerService(staff) {
                // 创建编辑模态框
                const modal = document.createElement('div');
                modal.className = 'modal';
                modal.style.display = 'flex';
                
                modal.innerHTML = `
                    <div class="modal-content" style="max-width: 500px; width: 90%;">
                        <div class="modal-header">
                            <h3>编辑客服</h3>
                            <span class="close-modal">&times;</span>
                        </div>
                        <div class="modal-body">
                            <form id="editCustomerServiceForm">
                                <div style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">客服ID</label>
                                    <input type="text" value="${staff.id}" disabled style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background-color: #f5f5f5;">
                                </div>
                                
                                <div style="margin-bottom: 15px;">
                                    <label for="editAccount" style="display: block; margin-bottom: 5px; font-weight: bold;">客服账号</label>
                                    <input type="text" id="editAccount" value="${staff.account}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" disabled>
                                </div>
                                
                                <div style="margin-bottom: 15px;">
                                    <label for="editPassword" style="display: block; margin-bottom: 5px; font-weight: bold;">密码</label>
                                    <input type="password" id="editPassword" placeholder="不修改请留空" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                                
                                <div style="margin-bottom: 15px;">
                                    <label for="editName" style="display: block; margin-bottom: 5px; font-weight: bold;">客服名称</label>
                                    <input type="text" id="editName" value="${staff.name}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                                </div>
                                
                                <div style="margin-bottom: 15px;">
                                    <label for="editEmail" style="display: block; margin-bottom: 5px; font-weight: bold;">邮箱</label>
                                    <input type="email" id="editEmail" value="${staff.email || ''}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                                </div>
                                
                                <div style="margin-bottom: 15px;">
                                    <label for="editPhone" style="display: block; margin-bottom: 5px; font-weight: bold;">电话</label>
                                    <input type="tel" id="editPhone" value="${staff.phone}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                                </div>
                                
                                <div style="margin-bottom: 15px;">
                                    <label for="editWechat" style="display: block; margin-bottom: 5px; font-weight: bold;">微信</label>
                                    <input type="text" id="editWechat" value="${staff.wechat || ''}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                                
                                <div style="margin-bottom: 15px;">
                                    <label for="editStatus" style="display: block; margin-bottom: 5px; font-weight: bold;">状态</label>
                                    <select id="editStatus" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                                        <option value="active" ${staff.status === 'active' ? 'selected' : ''}>在线</option>
                                        <option value="busy" ${staff.status === 'busy' ? 'selected' : ''}>忙碌</option>
                                        <option value="inactive" ${staff.status === 'inactive' ? 'selected' : ''}>离线</option>
                                    </select>
                                </div>
                                
                                <div style="text-align: right; margin-top: 20px;">
                                    <button type="button" class="btn-cancel" style="background-color: #f5f5f5; color: #333; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-right: 10px;">取消</button>
                                    <button type="submit" class="btn-submit" style="background-color: #28a745; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">保存</button>
                                </div>
                            </form>
                        </div>
                    </div>
                `;
                
                // 添加到页面
                document.body.appendChild(modal);
                
                // 添加关闭事件
                const closeModalBtn = modal.querySelector('.close-modal');
                const cancelBtn = modal.querySelector('.btn-cancel');
                
                closeModalBtn.addEventListener('click', () => {
                    document.body.removeChild(modal);
                });
                
                cancelBtn.addEventListener('click', () => {
                    document.body.removeChild(modal);
                });
                
                // 点击模态框外部关闭
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        document.body.removeChild(modal);
                    }
                });
                
                // 表单提交事件
                const editForm = modal.querySelector('#editCustomerServiceForm');
                if (editForm) {
                    editForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        
                        // 获取修改后的数据
                        const name = document.getElementById('editName').value;
                        const email = document.getElementById('editEmail').value;
                        const phone = document.getElementById('editPhone').value;
                        const wechat = document.getElementById('editWechat').value;
                        const status = document.getElementById('editStatus').value;
                        const password = document.getElementById('editPassword').value;
                        
                        // 构建更新数据对象
                        const updateData = {
                            name: name,
                            email: email,
                            phone: phone,
                            wechat: wechat,
                            status: status
                        };
                        
                        // 如果提供了新密码，则包含在更新中
                        if (password.trim()) {
                            updateData.password = password;
                        }
                        
                        // 发送到服务器
                        fetch(`/api/customer-service/${staff.id}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(updateData)
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // 在本地更新客服数据
                                const index = customerServiceStaff.findIndex(s => s.id === staff.id);
                                if (index !== -1) {
                                    // 使用返回的数据更新本地数据
                                    customerServiceStaff[index] = data.customerService;
                                }
                                
                                // 关闭模态框
                                document.body.removeChild(modal);
                                
                                // 显示成功提示
                                showToast(`客服 ${name} 信息已更新`, 'success', '更新成功');
                                
                                // 重新加载客服列表
                                loadCustomerServiceStaff();
                            } else {
                                showToast(data.message || '更新客服失败', 'error', '更新失败');
                            }
                        })
                        .catch(error => {
                            console.error('更新客服失败:', error);
                            showToast('网络错误，请稍后再试', 'error', '更新失败');
                        });
                    });
                }
            }
            
            // 登出功能
            const setupLogout = () => {
                const logoutBtn = document.getElementById('logoutBtn');
                
                if (logoutBtn) logoutBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    // 清除管理员会话信息
                    sessionStorage.removeItem('loggedInAdmin');
                    // 清除欢迎提示标记
                    sessionStorage.removeItem('welcomeShown');
                    
                    // 显示退出成功提示并重定向
                    showToast('您已成功退出登录', 'info', '退出登录');
                    
                    // 延迟跳转，让用户看到提示
                    setTimeout(() => {
                        window.location.href = 'admin-login.html';
                    }, 1500);
                });
            };
            
            // 显示优雅的toast通知
            window.showToast = function(message, type = 'info', title = '', duration = 5000) {
                const container = document.getElementById('toast-container');
                
                // 检查是否已经有相同内容的toast，避免重复显示
                const existingToasts = container.querySelectorAll('.toast');
                for (let i = 0; i < existingToasts.length; i++) {
                    const existingMessage = existingToasts[i].querySelector('.toast-message');
                    if (existingMessage && existingMessage.textContent === message) {
                        return; // 如果已有相同消息的通知，则不再创建新的
                    }
                }
                
                // 创建toast元素
                const toast = document.createElement('div');
                toast.className = `toast toast-${type}`;
                
                // 设置图标
                let iconClass = 'info-circle';
                if (type === 'success') iconClass = 'check-circle';
                if (type === 'error') iconClass = 'times-circle';
                if (type === 'warning') iconClass = 'exclamation-triangle';
                
                // 构建toast内容
                toast.innerHTML = `
                    <div class="toast-icon">
                        <i class="fas fa-${iconClass}"></i>
                    </div>
                    <div class="toast-content">
                        ${title ? `<div class="toast-title">${title}</div>` : ''}
                        <div class="toast-message">${message}</div>
                    </div>
                    <div class="toast-close">
                        <i class="fas fa-times"></i>
                    </div>
                    <div class="toast-progress">
                        <div class="toast-progress-bar"></div>
                    </div>
                `;
                
                // 添加到容器
                container.appendChild(toast);
                
                // 触发动画
                setTimeout(() => {
                    toast.classList.add('show');
                    
                    // 设置进度条动画
                    const progressBar = toast.querySelector('.toast-progress-bar');
                    progressBar.style.transition = `width ${duration}ms linear`;
                    progressBar.style.width = '0';
                }, 10);
                
                // 设置关闭事件
                const closeBtn = toast.querySelector('.toast-close');
                closeBtn.addEventListener('click', () => {
                    removeToast(toast);
                });
                
                // 自动关闭
                const closeTimeout = setTimeout(() => {
                    removeToast(toast);
                }, duration);
                
                // 移除Toast的函数
                function removeToast(toast) {
                    clearTimeout(closeTimeout);
                    toast.classList.remove('show');
                    
                    // 动画结束后移除DOM
                    setTimeout(() => {
                        if (container.contains(toast)) {
                            container.removeChild(toast);
                        }
                    }, 300);
                }
            };
            
            // 初始化页面
            initPage();
            setupLogout();

            // 设置黑名单用户卡片点击事件
            const blacklistedUserCard = document.getElementById('blacklistedUserCard');
            const blacklistModal = document.getElementById('blacklistModal');
            const closeModal = document.querySelector('.close-modal');
            
            if (blacklistedUserCard) {
                blacklistedUserCard.addEventListener('click', function() {
                    blacklistModal.style.display = 'flex';
                    loadBlacklistedUsers();
                });
            }
            
            if (closeModal) {
                closeModal.addEventListener('click', function() {
                    blacklistModal.style.display = 'none';
                });
            }
            
            // 点击模态框外部关闭
            window.addEventListener('click', function(event) {
                if (event.target === blacklistModal) {
                    blacklistModal.style.display = 'none';
                }
            });

            // 设置今日新增用户卡片点击事件
            const newUsersCard = document.getElementById('newUsersCard');
            const newUsersModal = document.getElementById('newUsersModal');
            const closeNewUsersModal = newUsersModal.querySelector('.close-modal');
            
            if (newUsersCard) {
                newUsersCard.addEventListener('click', function() {
                    newUsersModal.style.display = 'flex';
                    loadNewUsers();
                });
            }
            
            if (closeNewUsersModal) {
                closeNewUsersModal.addEventListener('click', function() {
                    newUsersModal.style.display = 'none';
                });
            }
            
            // 点击模态框外部关闭
            window.addEventListener('click', function(event) {
                if (event.target === newUsersModal) {
                    newUsersModal.style.display = 'none';
                }
            });
            
            // 加载今日新增用户数据
            const loadNewUsers = () => {
                fetch('/api/users?today=true')
                    .then(response => response.json())
                    .then(data => {
                        const newUsersTableBody = document.getElementById('newUsersTableBody');
                        if (!newUsersTableBody) return;
                        
                        if (!data.success) {
                            newUsersTableBody.innerHTML = `<tr><td colspan="5" class="table-empty">加载失败: ${data.message}</td></tr>`;
                            return;
                        }
                        
                        const users = data.users;
                        
                        if (!users || users.length === 0) {
                            newUsersTableBody.innerHTML = '<tr><td colspan="5" class="table-empty">今日暂无新增用户</td></tr>';
                            return;
                        }
                        
                        // 清空现有内容
                        newUsersTableBody.innerHTML = '';
                        
                        // 添加用户行
                        users.forEach(user => {
                            const row = document.createElement('tr');
                            
                            // 格式化注册时间为 HH:MM:SS
                            const registerDate = new Date(user.registrationTime);
                            const hours = String(registerDate.getHours()).padStart(2, '0');
                            const minutes = String(registerDate.getMinutes()).padStart(2, '0');
                            const seconds = String(registerDate.getSeconds()).padStart(2, '0');
                            const formattedTime = `${hours}:${minutes}:${seconds}`;
                            
                            // 确定用户联系方式（邮箱或微信）
                            const contactInfo = user.email ? user.email : (user.wechat ? user.wechat : '未提供');
                            
                            row.innerHTML = `
                                <td>${user.id}</td>
                                <td>${user.username}</td>
                                <td>${contactInfo}</td>
                                <td>${formattedTime}</td>
                                <td>
                                    <button class="btn-view" data-id="${user.id}">查看</button>
                                    <button class="btn-blacklist" data-id="${user.id}">${user.blacklisted ? '解除拉黑' : '拉黑'}</button>
                                </td>
                            `;
                            
                            newUsersTableBody.appendChild(row);
                        });
                        
                        // 添加事件监听
                        setupUserActionButtons();
                    })
                    .catch(error => {
                        console.error('获取今日新增用户数据失败:', error);
                        const newUsersTableBody = document.getElementById('newUsersTableBody');
                        if (newUsersTableBody) {
                            newUsersTableBody.innerHTML = '<tr><td colspan="5" class="table-empty">加载今日新增用户数据失败，请刷新重试</td></tr>';
                        }
                        showToast('获取今日新增用户数据失败', 'error');
                    });
            };

            // 设置用户注册趋势图相关功能
            const setupRegistrationChart = () => {
                const totalUsersCard = document.getElementById('totalUsersCard');
                const registrationChartModal = document.getElementById('registrationChartModal');
                const closeChartModal = registrationChartModal.querySelector('.close-modal');
                let registrationChart = null;
                
                // 保存当前选择的天数范围，默认为7天
                let currentDaysRange = parseInt(localStorage.getItem('chartDaysRange') || '7');
                
                // 点击总用户数卡片显示趋势图
                if (totalUsersCard) {
                    totalUsersCard.addEventListener('click', function() {
                        registrationChartModal.style.display = 'flex';
                        
                        // 使用保存的天数范围加载图表
                        loadRegistrationChart(currentDaysRange);
                        
                        // 更新按钮激活状态
                        updateRangeButtonsState(currentDaysRange);
                    });
                }
                
                // 更新范围按钮状态
                const updateRangeButtonsState = (days) => {
                    const rangeButtons = document.querySelectorAll('.chart-range-btn');
                    if (!rangeButtons) return;
                    
                    rangeButtons.forEach(btn => {
                        const btnDays = parseInt(btn.getAttribute('data-range'));
                        if (btnDays === days) {
                            btn.classList.add('active');
                        } else {
                            btn.classList.remove('active');
                        }
                    });
                };
                
                // 关闭按钮事件
                if (closeChartModal) {
                    closeChartModal.addEventListener('click', function() {
                        registrationChartModal.style.display = 'none';
                    });
                }
                
                // 点击模态框外部关闭
                window.addEventListener('click', function(event) {
                    if (event.target === registrationChartModal) {
                        registrationChartModal.style.display = 'none';
                    }
                });
                
                // 时间范围按钮事件
                const rangeButtons = document.querySelectorAll('.chart-range-btn');
                if (rangeButtons) {
                    rangeButtons.forEach(button => {
                        button.addEventListener('click', function() {
                            // 移除所有按钮的active类
                            rangeButtons.forEach(btn => btn.classList.remove('active'));
                            // 添加当前按钮的active类
                            this.classList.add('active');
                            // 获取天数范围
                            const days = parseInt(this.getAttribute('data-range'));
                            // 保存当前选择的天数
                            currentDaysRange = days;
                            localStorage.setItem('chartDaysRange', days.toString());
                            // 重新加载图表
                            loadRegistrationChart(days);
                        });
                    });
                }
                
                // 全局变量，用于跟踪滚动条拖动状态
                let isDragging = false;
                let startX, startLeft;
                
                // 设置滚动条拖动事件
                const setupScrollThumbDrag = () => {
                    const scrollThumb = document.querySelector('.chart-scroll-thumb');
                    const scrollTrack = document.querySelector('.chart-scroll-track');
                    
                    if (!scrollThumb || !scrollTrack) return;
                    
                    // 清除之前可能的事件监听器
                    scrollThumb.removeEventListener('mousedown', handleScrollThumbMouseDown);
                    scrollTrack.removeEventListener('click', handleScrollTrackClick);
                    
                    // 添加新的事件监听器
                    scrollThumb.addEventListener('mousedown', handleScrollThumbMouseDown);
                    scrollTrack.addEventListener('click', handleScrollTrackClick);
                };
                
                // 滚动条拖动开始处理
                const handleScrollThumbMouseDown = function(e) {
                    const scrollThumb = e.target;
                    isDragging = true;
                    startX = e.clientX;
                    startLeft = parseFloat(scrollThumb.style.left || '0');
                    e.preventDefault();
                };
                
                // 滚动条轨道点击处理
                const handleScrollTrackClick = function(e) {
                    const scrollThumb = document.querySelector('.chart-scroll-thumb');
                    const scrollTrack = document.querySelector('.chart-scroll-track');
                    
                    if (!scrollThumb || !scrollTrack || e.target === scrollThumb) return;
                    
                    const trackRect = scrollTrack.getBoundingClientRect();
                    const clickPosition = (e.clientX - trackRect.left) / trackRect.width;
                    
                    const chartData = registrationChart.data;
                    const totalItems = chartData.labels.length;
                    const visibleItems = chartData.labels.length;
                    
                    // 计算新的开始位置
                    let newStart = Math.floor(clickPosition * (totalItems - visibleItems));
                    newStart = Math.max(0, Math.min(newStart, totalItems - visibleItems));
                    
                    // 更新视图状态
                    if (window.chartViewState) {
                        window.chartViewState.start = newStart;
                        window.chartViewState.end = newStart + visibleItems;
                        
                        // 更新图表
                        updateChartData();
                    }
                };
                
                // 添加全局鼠标移动和鼠标释放事件
                document.addEventListener('mousemove', function(e) {
                    if (!isDragging) return;
                    
                    const scrollThumb = document.querySelector('.chart-scroll-thumb');
                    const scrollTrack = document.querySelector('.chart-scroll-track');
                    
                    if (!scrollThumb || !scrollTrack) {
                        isDragging = false;
                        return;
                    }
                    
                    const trackWidth = scrollTrack.offsetWidth;
                    const thumbWidth = scrollThumb.offsetWidth;
                    
                    // 计算新位置
                    const dx = e.clientX - startX;
                    const percentMoved = dx / trackWidth * 100;
                    let newLeft = startLeft + percentMoved;
                    
                    // 限制范围
                    newLeft = Math.max(0, Math.min(newLeft, (100 - (thumbWidth / trackWidth * 100))));
                    
                    // 更新滑块位置
                    scrollThumb.style.left = `${newLeft}%`;
                    
                    // 如果有图表和视图状态，更新数据
                    if (registrationChart && window.chartViewState) {
                        const chartData = registrationChart.data;
                        const totalItems = window.chartOriginalData.labels.length;
                        const visibleItems = window.chartViewState.end - window.chartViewState.start;
                        
                        // 计算对应的数据范围
                        const newStart = Math.floor((newLeft / 100) * (totalItems - visibleItems));
                        
                        // 更新视图状态
                        window.chartViewState.start = newStart;
                        window.chartViewState.end = newStart + visibleItems;
                        
                        // 更新图表数据
                        updateChartData();
                    }
                });
                
                document.addEventListener('mouseup', function() {
                    isDragging = false;
                });
                
                // 加载用户注册趋势图
                const loadRegistrationChart = async (days) => {
                    try {
                        // 获取注册数据
                        const registrationData = await fetchRegistrationData(days);
                        // 渲染图表
                        renderRegistrationChart(registrationData, days);
                        // 设置滚动条拖动
                        setTimeout(setupScrollThumbDrag, 100);
                    } catch (error) {
                        console.error('加载用户注册趋势图失败:', error);
                        showToast('加载趋势图失败，请稍后重试', 'error');
                    }
                };
                
                // 获取用户注册数据
                const fetchRegistrationData = async (days) => {
                    try {
                        // 调用后端API获取真实数据
                        const response = await fetch(`/api/users-registration-trend?days=${days}`);
                        const result = await response.json();
                        
                        if (result.success && result.data) {
                            return result.data;
                        } else {
                            throw new Error(result.message || '获取数据失败');
                        }
                    } catch (error) {
                        console.error('获取用户注册趋势数据失败:', error);
                        showToast('获取用户注册趋势数据失败，使用模拟数据替代', 'warning');
                        
                        // 如果API调用失败，使用模拟数据作为备选
                        // 生成日期数组
                        const dates = [];
                        const counts = [];
                        const now = new Date();
                        
                        // 随机波动基数
                        const baseCount = 5 + Math.floor(Math.random() * 10);
                        
                        for (let i = days - 1; i >= 0; i--) {
                            const date = new Date(now);
                            date.setDate(date.getDate() - i);
                            
                            // 格式化日期为 MM-DD 格式
                            const month = String(date.getMonth() + 1).padStart(2, '0');
                            const day = String(date.getDate()).padStart(2, '0');
                            const formattedDate = `${month}-${day}`;
                            
                            dates.push(formattedDate);
                            
                            // 生成随机注册数量
                            let dayOfWeek = date.getDay();
                            let isWeekend = (dayOfWeek === 0 || dayOfWeek === 6);
                            
                            let count = baseCount + Math.floor(Math.random() * 15);
                            if (isWeekend) {
                                count += Math.floor(Math.random() * 8);
                            }
                            
                            counts.push(count);
                        }
                        
                        return { dates, counts };
                    }
                };
                
                // 渲染用户注册趋势图
                const renderRegistrationChart = (data, days) => {
                    const ctx = document.getElementById('registrationChart').getContext('2d');
                    
                    // 如果图表已存在，先销毁
                    if (registrationChart) {
                        registrationChart.destroy();
                    }
                    
                    // 计算Y轴最大值，确保有一定空间
                    const maxCount = Math.max(...data.counts);
                    const yMax = Math.ceil(maxCount * 1.2);
                    
                    // 保存原始数据，用于缩放重置
                    const originalData = {
                        labels: [...data.labels || data.dates],
                        counts: [...data.counts]
                    };
                    
                    // 将数据保存到window对象，以便在全局访问
                    window.chartOriginalData = originalData;
                    
                    // 当前视图状态
                    window.chartViewState = {
                        start: 0,
                        end: data.dates.length,
                        zoomLevel: 1
                    };
                    
                    // 更新滚动条状态
                    const updateScrollThumb = () => {
                        const scrollContainer = document.getElementById('chartScrollContainer');
                        const scrollThumb = document.querySelector('.chart-scroll-thumb');
                        
                        if (!scrollThumb) return;
                        
                        const totalItems = window.chartOriginalData.labels.length;
                        const visibleItems = window.chartViewState.end - window.chartViewState.start;
                        
                        // 计算滑块宽度比例
                        const thumbWidthPercent = Math.max(5, (visibleItems / totalItems) * 100);
                        
                        // 计算滑块位置
                        const thumbPositionPercent = (window.chartViewState.start / totalItems) * (100 - thumbWidthPercent);
                        
                        // 应用样式
                        scrollThumb.style.width = `${thumbWidthPercent}%`;
                        scrollThumb.style.left = `${thumbPositionPercent}%`;
                        
                        // 显示或隐藏滚动条
                        if (window.chartViewState.zoomLevel > 1) {
                            scrollContainer.style.display = 'block';
                        } else {
                            scrollContainer.style.display = 'none';
                        }
                    };
                    
                    // 更新图表数据
                    window.updateChartData = () => {
                        if (!registrationChart || !window.chartOriginalData || !window.chartViewState) return;
                        
                        // 获取当前视图的数据子集
                        const visibleLabels = window.chartOriginalData.labels.slice(window.chartViewState.start, window.chartViewState.end);
                        const visibleCounts = window.chartOriginalData.counts.slice(window.chartViewState.start, window.chartViewState.end);
                        
                        // 更新图表数据
                        registrationChart.data.labels = visibleLabels;
                        registrationChart.data.datasets[0].data = visibleCounts;
                        
                        // 更新图表
                        registrationChart.update();
                        
                        // 更新滚动条
                        updateScrollThumb();
                    };
                    
                    // 处理缩放
                    const handleZoom = (zoomIn) => {
                        const totalItems = window.chartOriginalData.labels.length;
                        const currentVisible = window.chartViewState.end - window.chartViewState.start;
                        
                        // 计算新的可见项目数量
                        let newVisible;
                        if (zoomIn) {
                            // 放大 - 减少可见项目
                            newVisible = Math.max(Math.floor(currentVisible * 0.7), Math.min(10, totalItems));
                            window.chartViewState.zoomLevel = totalItems / newVisible;
                        } else {
                            // 缩小 - 增加可见项目
                            newVisible = Math.min(Math.ceil(currentVisible * 1.5), totalItems);
                            window.chartViewState.zoomLevel = totalItems / newVisible;
                        }
                        
                        // 如果是缩小到最小级别，则重置视图
                        if (newVisible >= totalItems) {
                            window.chartViewState.start = 0;
                            window.chartViewState.end = totalItems;
                            window.chartViewState.zoomLevel = 1;
                            window.updateChartData();
                            return;
                        }
                        
                        // 计算新的开始和结束索引，保持中心点不变
                        const currentCenter = window.chartViewState.start + currentVisible / 2;
                        const newStart = Math.max(0, Math.floor(currentCenter - newVisible / 2));
                        const newEnd = Math.min(totalItems, newStart + newVisible);
                        
                        // 更新视图状态
                        window.chartViewState.start = newStart;
                        window.chartViewState.end = newEnd;
                        
                        // 更新图表
                        window.updateChartData();
                    };
                    
                    // 处理平移
                    const handlePan = (direction) => {
                        const totalItems = window.chartOriginalData.labels.length;
                        const currentVisible = window.chartViewState.end - window.chartViewState.start;
                        
                        // 计算移动的步长（可见项目的10%）
                        const step = Math.max(1, Math.floor(currentVisible * 0.1));
                        
                        if (direction === 'left') {
                            // 向左移动
                            window.chartViewState.start = Math.max(0, window.chartViewState.start - step);
                            window.chartViewState.end = window.chartViewState.start + currentVisible;
                        } else {
                            // 向右移动
                            window.chartViewState.end = Math.min(totalItems, window.chartViewState.end + step);
                            window.chartViewState.start = window.chartViewState.end - currentVisible;
                        }
                        
                        // 确保不超出边界
                        if (window.chartViewState.start < 0) window.chartViewState.start = 0;
                        if (window.chartViewState.end > totalItems) window.chartViewState.end = totalItems;
                        
                        // 更新图表
                        window.updateChartData();
                    };
                    
                    // 重置缩放
                    const resetZoom = () => {
                        window.chartViewState.start = 0;
                        window.chartViewState.end = window.chartOriginalData.labels.length;
                        window.chartViewState.zoomLevel = 1;
                        window.updateChartData();
                    };
                    
                    // 获取天数范围的文本描述
                    const getDaysRangeText = (days) => {
                        if (days === 7) return '最近7天';
                        if (days === 30) return '最近30天';
                        if (days === 90) return '最近90天';
                        if (days === 365) return '最近一年';
                        return `最近${days}天`;
                    };
                    
                    // 创建新图表
                    registrationChart = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: data.dates,
                            datasets: [{
                                label: '每日注册用户数',
                                data: data.counts,
                                backgroundColor: 'rgba(12, 77, 162, 0.2)',
                                borderColor: 'rgba(12, 77, 162, 1)',
                                borderWidth: 2,
                                pointBackgroundColor: 'rgba(12, 77, 162, 1)',
                                pointBorderColor: '#fff',
                                pointBorderWidth: 1,
                                pointRadius: 4,
                                pointHoverRadius: 6,
                                fill: true,
                                tension: 0.2
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: {
                                    display: true,
                                    text: getDaysRangeText(days) + '用户注册趋势',
                                    font: {
                                        size: 16,
                                        weight: 'bold'
                                    },
                                    padding: {
                                        top: 10,
                                        bottom: 20
                                    }
                                },
                                legend: {
                                    display: true,
                                    position: 'top'
                                },
                                tooltip: {
                                    mode: 'index',
                                    intersect: false,
                                    callbacks: {
                                        title: function(tooltipItems) {
                                            return `${tooltipItems[0].label} 注册情况`;
                                        },
                                        label: function(context) {
                                            return `新增用户: ${context.raw} 人`;
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: yMax,
                                    ticks: {
                                        precision: 0
                                    },
                                    title: {
                                        display: true,
                                        text: '注册人数'
                                    }
                                },
                                x: {
                                    title: {
                                        display: true,
                                        text: '日期'
                                    }
                                }
                            },
                            interaction: {
                                intersect: false,
                                mode: 'nearest'
                            }
                        }
                    });
                    
                    // 绑定缩放按钮事件
                    const zoomInBtn = document.getElementById('zoomInBtn');
                    const zoomOutBtn = document.getElementById('zoomOutBtn');
                    const resetZoomBtn = document.getElementById('resetZoomBtn');
                    
                    if (zoomInBtn) {
                        zoomInBtn.onclick = () => handleZoom(true);
                    }
                    
                    if (zoomOutBtn) {
                        zoomOutBtn.onclick = () => handleZoom(false);
                    }
                    
                    if (resetZoomBtn) {
                        resetZoomBtn.onclick = resetZoom;
                    }
                    
                    // 键盘导航
                    document.addEventListener('keydown', function(e) {
                        // 只有当图表模态框显示时才处理键盘事件
                        const chartModal = document.getElementById('registrationChartModal');
                        if (chartModal.style.display !== 'flex') return;
                        
                        switch (e.key) {
                            case 'ArrowLeft':
                                handlePan('left');
                                break;
                            case 'ArrowRight':
                                handlePan('right');
                                break;
                            case '+':
                            case '=':
                                handleZoom(true);
                                break;
                            case '-':
                                handleZoom(false);
                                break;
                            case '0':
                                resetZoom();
                                break;
                        }
                    });
                    
                    // 初始化滚动条
                    updateScrollThumb();
                };
            };
            
                                // 初始化注册趋势图
                    setupRegistrationChart();
                    
                    // 添加下载表格功能
                    const downloadChartData = document.getElementById('downloadChartData');
                    if (downloadChartData) {
                        downloadChartData.addEventListener('click', function() {
                            if (!window.chartOriginalData) {
                                showToast('没有可下载的数据', 'warning');
                                return;
                            }
                            
                            // 获取当前选择的天数范围
                            const currentDaysRange = parseInt(localStorage.getItem('chartDaysRange') || '7');
                            
                            // 根据当前选择的图表范围获取完整数据
                            // 如果需要获取更多历史数据，这里应该调用API
                            const fetchCompleteData = async () => {
                                try {
                                    // 调用API获取完整的历史数据
                                    const response = await fetch(`/api/users-registration-trend?days=${currentDaysRange}`);
                                    const result = await response.json();
                                    
                                    if (result.success && result.data) {
                                        return result.data;
                                    } else {
                                        // 如果API调用失败，使用当前图表数据
                                        return {
                                            dates: window.chartOriginalData.labels,
                                            counts: window.chartOriginalData.counts
                                        };
                                    }
                                } catch (error) {
                                    console.error('获取完整历史数据失败:', error);
                                    // 出错时使用当前图表数据
                                    return {
                                        dates: window.chartOriginalData.labels,
                                        counts: window.chartOriginalData.counts
                                    };
                                }
                            };
                            
                            // 获取完整数据并下载
                            fetchCompleteData().then(data => {
                                // 获取图表数据
                                const labels = data.dates || data.labels;
                                const counts = data.counts;
                                
                                // 创建CSV内容
                                let csvContent = "日期,注册人数\n";
                                
                                // 添加数据行
                                for (let i = 0; i < labels.length; i++) {
                                    csvContent += `${labels[i]},${counts[i]}\n`;
                                }
                                
                                // 创建Blob对象
                                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                                
                                // 创建下载链接
                                const link = document.createElement('a');
                                const url = URL.createObjectURL(blob);
                                
                                // 设置下载属性
                                const rangeText = currentDaysRange === 7 ? '7天' : 
                                                currentDaysRange === 30 ? '30天' : 
                                                currentDaysRange === 90 ? '90天' : 
                                                currentDaysRange === 365 ? '1年' : `${currentDaysRange}天`;
                                
                                link.setAttribute('href', url);
                                link.setAttribute('download', `用户注册趋势数据_最近${rangeText}_${new Date().toISOString().split('T')[0]}.csv`);
                                link.style.display = 'none';
                                
                                // 添加到DOM并触发点击
                                document.body.appendChild(link);
                                link.click();
                                
                                // 清理
                                document.body.removeChild(link);
                                URL.revokeObjectURL(url);
                                
                                showToast(`最近${rangeText}数据已下载为CSV格式`, 'success');
                            });
                        });
                    }
                });
            </script>
        </body>
        </html>  